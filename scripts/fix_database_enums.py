#!/usr/bin/env python3
"""
Comprehensive database enum fix script.
This script will update PostgreSQL enums to match the application enum values.
"""
import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def fix_database_enums():
    """Fix all database enums to match application definitions."""
    try:
        print("🔧 Fixing database enums...")
        
        from app.database import database_service
        from sqlalchemy import text
        
        # Initialize database connection
        await database_service.initialize()
        
        async with database_service.engine.begin() as conn:
            # Define expected enum values based on the application code
            enum_fixes = {
                'jobstatus': ['pending', 'processing', 'completed', 'failed'],
                'mediatype': ['video', 'audio', 'image', 'document', 'other'],
                'mediacategory': [
                    'footage_to_video', 'aiimage_to_video', 'scenes_to_video', 
                    'short_video_creation', 'image_to_video', 'text_to_speech',
                    'music_generation', 'audio_transcription', 'voice_cloning',
                    'image_generation', 'image_editing', 'image_upscaling',
                    'media_download', 'media_conversion', 'metadata_extraction',
                    'youtube_transcript', 'other'
                ],
                'videotype': [
                    'footage_to_video', 'aiimage_to_video', 'scenes_to_video',
                    'short_video_creation', 'image_to_video', 'other'
                ]
            }
            
            for enum_name, expected_values in enum_fixes.items():
                print(f"\n📊 Checking {enum_name} enum...")
                
                # Check if enum type exists
                result = await conn.execute(text(f"""
                    SELECT EXISTS(SELECT 1 FROM pg_type WHERE typname = '{enum_name}');
                """))
                enum_exists = result.scalar()
                
                if not enum_exists:
                    print(f"⚠️ Enum type '{enum_name}' does not exist, creating it...")
                    # Create the enum type
                    values_str = "', '".join(expected_values)
                    await conn.execute(text(f"""
                        CREATE TYPE {enum_name} AS ENUM ('{values_str}');
                    """))
                    print(f"✅ Created enum type '{enum_name}'")
                    continue
                
                # Get current enum values
                result = await conn.execute(text(f"""
                    SELECT enumlabel 
                    FROM pg_enum 
                    WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = '{enum_name}')
                    ORDER BY enumlabel;
                """))
                current_values = [row[0] for row in result.fetchall()]
                print(f"Current {enum_name} values: {current_values}")
                
                # Find missing values
                missing_values = [v for v in expected_values if v not in current_values]
                
                if not missing_values:
                    print(f"✅ {enum_name} enum already has correct values!")
                    continue
                
                print(f"🔨 Adding missing {enum_name} values: {missing_values}")
                
                # Add missing enum values
                for value in missing_values:
                    try:
                        await conn.execute(text(f"ALTER TYPE {enum_name} ADD VALUE '{value}';"))
                        print(f"✅ Added {enum_name} value: {value}")
                    except Exception as e:
                        if "already exists" in str(e):
                            print(f"ℹ️ {enum_name} value '{value}' already exists")
                        else:
                            print(f"❌ Failed to add {enum_name} value '{value}': {e}")
                
                # Verify the fix
                result = await conn.execute(text(f"""
                    SELECT enumlabel 
                    FROM pg_enum 
                    WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = '{enum_name}')
                    ORDER BY enumlabel;
                """))
                final_values = [row[0] for row in result.fetchall()]
                print(f"Final {enum_name} values: {final_values}")
            
            print("\n✅ All database enum fixes completed!")
            return True
        
        await database_service.close()
        
    except Exception as e:
        print(f"❌ Failed to fix database enums: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_database_enums())
    sys.exit(0 if success else 1)
