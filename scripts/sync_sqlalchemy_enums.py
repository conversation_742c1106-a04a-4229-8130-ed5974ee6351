#!/usr/bin/env python3
"""
Synchronize SQLAlchemy enum definitions with existing PostgreSQL enums.
This script ensures SQLAlchemy recognizes the existing enum types in the database.
"""
import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def sync_sqlalchemy_enums():
    """Synchronize SQLAlchemy enum definitions with database."""
    try:
        print("🔧 Synchronizing SQLAlchemy enums with database...")
        
        from app.database import database_service
        from sqlalchemy import text, MetaData
        
        # Initialize database connection
        await database_service.initialize()
        
        async with database_service.engine.begin() as conn:
            print("\n📊 Checking current database enum types...")
            
            # Get all enum types and their values from the database
            result = await conn.execute(text("""
                SELECT 
                    t.typname as enum_name,
                    array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values
                FROM pg_type t 
                JOIN pg_enum e ON t.oid = e.enumtypid 
                WHERE t.typtype = 'e'
                GROUP BY t.typname
                ORDER BY t.typname;
            """))
            
            db_enums = {}
            for row in result.fetchall():
                enum_name, enum_values = row
                db_enums[enum_name] = enum_values
                print(f"Database enum '{enum_name}': {enum_values}")
            
            print("\n🔍 Checking SQLAlchemy enum definitions...")
            
            from app.database import JobStatus, MediaType, MediaCategory
            
            # Check JobStatus
            app_jobstatus = [e.value for e in JobStatus]
            db_jobstatus = db_enums.get('jobstatus', [])
            print(f"App JobStatus: {app_jobstatus}")
            print(f"DB jobstatus: {db_jobstatus}")
            
            if set(app_jobstatus) != set(db_jobstatus):
                print("⚠️ JobStatus enum mismatch detected!")
                missing_in_db = set(app_jobstatus) - set(db_jobstatus)
                missing_in_app = set(db_jobstatus) - set(app_jobstatus)
                if missing_in_db:
                    print(f"Missing in DB: {missing_in_db}")
                if missing_in_app:
                    print(f"Missing in App: {missing_in_app}")
            else:
                print("✅ JobStatus enum matches!")
            
            # Check MediaType
            app_mediatype = [e.value for e in MediaType]
            db_mediatype = db_enums.get('mediatype', [])
            print(f"App MediaType: {app_mediatype}")
            print(f"DB mediatype: {db_mediatype}")
            
            if set(app_mediatype) != set(db_mediatype):
                print("⚠️ MediaType enum mismatch detected!")
                missing_in_db = set(app_mediatype) - set(db_mediatype)
                missing_in_app = set(db_mediatype) - set(app_mediatype)
                if missing_in_db:
                    print(f"Missing in DB: {missing_in_db}")
                if missing_in_app:
                    print(f"Missing in App: {missing_in_app}")
            else:
                print("✅ MediaType enum matches!")
            
            # Check MediaCategory
            app_mediacategory = [e.value for e in MediaCategory]
            db_mediacategory = db_enums.get('mediacategory', [])
            print(f"App MediaCategory: {app_mediacategory}")
            print(f"DB mediacategory: {db_mediacategory}")
            
            if set(app_mediacategory) != set(db_mediacategory):
                print("⚠️ MediaCategory enum mismatch detected!")
                missing_in_db = set(app_mediacategory) - set(db_mediacategory)
                missing_in_app = set(db_mediacategory) - set(app_mediacategory)
                if missing_in_db:
                    print(f"Missing in DB: {missing_in_db}")
                if missing_in_app:
                    print(f"Missing in App: {missing_in_app}")
            else:
                print("✅ MediaCategory enum matches!")
            
            print("\n🔧 Testing SQLAlchemy metadata reflection...")
            
            # Reflect the database schema to see what SQLAlchemy sees
            metadata = MetaData()
            await conn.run_sync(metadata.reflect)
            
            print(f"Tables reflected: {list(metadata.tables.keys())}")
            
            # Check if the jobs table exists and has the correct column types
            if 'jobs' in metadata.tables:
                jobs_table = metadata.tables['jobs']
                status_column = jobs_table.columns.get('status')
                if status_column:
                    print(f"Jobs.status column type: {status_column.type}")
                else:
                    print("❌ Jobs.status column not found!")
            
            if 'media_library' in metadata.tables:
                media_table = metadata.tables['media_library']
                media_type_column = media_table.columns.get('media_type')
                category_column = media_table.columns.get('category')
                if media_type_column:
                    print(f"MediaLibrary.media_type column type: {media_type_column.type}")
                if category_column:
                    print(f"MediaLibrary.category column type: {category_column.type}")
            
            print("\n✅ SQLAlchemy enum synchronization check completed!")
            return True
        
        await database_service.close()
        
    except Exception as e:
        print(f"❌ Failed to sync SQLAlchemy enums: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(sync_sqlalchemy_enums())
    sys.exit(0 if success else 1)
