#!/usr/bin/env python3
"""
Test database enum usage to identify the exact issue.
"""
import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_database_enums():
    """Test database enum usage."""
    try:
        print("🔍 Testing database enum usage...")
        
        from app.database import database_service, JobStatus, MediaType, MediaCategory
        from sqlalchemy import text
        
        # Initialize database connection
        await database_service.initialize()
        
        async with database_service.engine.begin() as conn:
            print("\n📊 Testing JobStatus enum...")
            
            # Test direct enum value insertion
            try:
                result = await conn.execute(text("""
                    SELECT 'pending'::jobstatus as test_value;
                """))
                value = result.scalar()
                print(f"✅ Direct enum cast works: {value}")
            except Exception as e:
                print(f"❌ Direct enum cast failed: {e}")
            
            # Test all JobStatus values
            for status in JobStatus:
                try:
                    result = await conn.execute(text(f"""
                        SELECT '{status.value}'::jobstatus as test_value;
                    """))
                    value = result.scalar()
                    print(f"✅ JobStatus.{status.name} ({status.value}) works")
                except Exception as e:
                    print(f"❌ JobStatus.{status.name} ({status.value}) failed: {e}")
            
            print("\n📊 Testing MediaType enum...")
            
            # Test all MediaType values
            for media_type in MediaType:
                try:
                    result = await conn.execute(text(f"""
                        SELECT '{media_type.value}'::mediatype as test_value;
                    """))
                    value = result.scalar()
                    print(f"✅ MediaType.{media_type.name} ({media_type.value}) works")
                except Exception as e:
                    print(f"❌ MediaType.{media_type.name} ({media_type.value}) failed: {e}")
            
            print("\n📊 Testing MediaCategory enum...")
            
            # Test a few MediaCategory values
            test_categories = [MediaCategory.FOOTAGE_TO_VIDEO, MediaCategory.IMAGE_GENERATION, MediaCategory.OTHER]
            for category in test_categories:
                try:
                    result = await conn.execute(text(f"""
                        SELECT '{category.value}'::mediacategory as test_value;
                    """))
                    value = result.scalar()
                    print(f"✅ MediaCategory.{category.name} ({category.value}) works")
                except Exception as e:
                    print(f"❌ MediaCategory.{category.name} ({category.value}) failed: {e}")
            
            print("\n📊 Testing SQLAlchemy enum usage...")
            
            # Test creating a job record
            try:
                from app.database import JobRecord
                from sqlalchemy.orm import sessionmaker
                
                Session = sessionmaker(bind=database_service.engine)
                async with Session() as session:
                    # Try to query jobs with enum filter
                    result = await session.execute(text("""
                        SELECT COUNT(*) FROM jobs WHERE status = 'pending'::jobstatus;
                    """))
                    count = result.scalar()
                    print(f"✅ Query with enum filter works: {count} pending jobs")
                    
            except Exception as e:
                print(f"❌ SQLAlchemy enum usage failed: {e}")
                import traceback
                traceback.print_exc()
            
            print("\n✅ Database enum testing completed!")
            return True
        
        await database_service.close()
        
    except Exception as e:
        print(f"❌ Failed to test database enums: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_database_enums())
    sys.exit(0 if success else 1)
