#!/usr/bin/env python3
"""
Fix database enum values for JobStatus.
This script will update the PostgreSQL enum to match the application enum values.
"""
import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def fix_jobstatus_enum():
    """Fix the jobstatus enum in the database."""
    try:
        print("🔧 Fixing JobStatus enum in database...")
        
        from app.database import database_service
        from sqlalchemy import text
        
        # Initialize database connection
        await database_service.initialize()
        
        async for session in database_service.get_session():
            try:
                # Check current enum values
                print("📊 Checking current enum values...")
                result = await session.execute(text("""
                    SELECT enumlabel 
                    FROM pg_enum 
                    WHERE enumtypid = (
                        SELECT oid FROM pg_type WHERE typname = 'jobstatus'
                    );
                """))
                current_values = [row[0] for row in result.fetchall()]
                print(f"Current enum values: {current_values}")
                
                expected_values = ['pending', 'processing', 'completed', 'failed']
                missing_values = [v for v in expected_values if v not in current_values]
                
                if not missing_values:
                    print("✅ JobStatus enum already has correct values!")
                    return True
                
                print(f"🔨 Adding missing enum values: {missing_values}")
                
                # Add missing enum values
                for value in missing_values:
                    try:
                        await session.execute(text(f"ALTER TYPE jobstatus ADD VALUE '{value}';"))
                        print(f"✅ Added enum value: {value}")
                    except Exception as e:
                        if "already exists" in str(e):
                            print(f"ℹ️ Enum value '{value}' already exists")
                        else:
                            print(f"❌ Failed to add enum value '{value}': {e}")
                
                await session.commit()
                
                # Verify the fix
                result = await session.execute(text("""
                    SELECT enumlabel 
                    FROM pg_enum 
                    WHERE enumtypid = (
                        SELECT oid FROM pg_type WHERE typname = 'jobstatus'
                    );
                """))
                final_values = [row[0] for row in result.fetchall()]
                print(f"Final enum values: {final_values}")
                
                print("✅ JobStatus enum fix completed!")
                return True
                
            except Exception as e:
                print(f"❌ Error fixing enum: {e}")
                await session.rollback()
                return False
            finally:
                break
        
        await database_service.close()
        
    except Exception as e:
        print(f"❌ Failed to fix JobStatus enum: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_jobstatus_enum())
    sys.exit(0 if success else 1)
