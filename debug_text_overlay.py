#!/usr/bin/env python3
"""
Debug script to test text overlay filter generation
"""
import sys
import os
sys.path.append('/home/<USER>/DEV.ai/Projects/ouinhi')

from app.services.video.text_overlay import TextOverlayService

def debug_filter_chain():
    """Debug the filter chain generation"""
    service = TextOverlayService()
    
    # Test basic text overlay
    text = "Hello there"
    options = {
        'font_size': 48,
        'font_color': 'white',
        'position': 'bottom-center',
        'y_offset': 50,
        'duration': 5,
        'start_time': 0.0,
        'box_color': 'black',
        'box_opacity': 0.8,
        'boxborderw': 60,
        'line_spacing': 9
    }
    
    print("Testing filter chain generation...")
    print(f"Text: {text}")
    print(f"Options: {options}")
    print()
    
    try:
        # Test with modern typography options (should use advanced filter)
        modern_options = {
            'typography': {
                'font_size': 48,
                'font_color': 'white',
                'font_family': 'roboto',
                'font_weight': 'regular'
            },
            'effects': {
                'background_enabled': True,
                'background_color': 'black@0.8',
                'background_padding': 60
            },
            'duration': 5,
            'position': 'bottom-center',
            'y_offset': 50,
            'line_spacing': 9
        }
        
        print("=== ADVANCED FILTER (with typography) ===")
        advanced_filter = service.build_advanced_filter_chain(text, modern_options)
        print(f"Advanced filter: {advanced_filter}")
        print(f"Filter length: {len(advanced_filter)}")
        print()
        
        print("=== LEGACY FILTER (without typography) ===")
        legacy_filter = service.build_legacy_compatible_filter(text, options)
        print(f"Legacy filter: {legacy_filter}")
        print(f"Legacy filter length: {len(legacy_filter)}")
        print()
        
        # Test the get_font_file method directly (this is used in fallback)
        print("=== FONT FILE TESTING ===")
        font_file = service.get_font_file(text)
        print(f"Font file for text '{text}': {font_file}")
        print()
        
        # Test with empty/problematic typography to trigger fallback
        print("=== TESTING FALLBACK SCENARIO ===")
        problematic_options = {
            'font_size': 48,
            'font_color': 'white',
            'position': 'bottom-center',  
            'y_offset': 50,
            'duration': 5,
            'start_time': 0.0,
            'box': 1,
            'boxcolor': 'black@0.8',
            'boxborderw': 60,
            'line_spacing': 9
        }
        fallback_filter = service.build_legacy_compatible_filter(text, problematic_options)
        print(f"Fallback filter: {fallback_filter}")
        print()
        
        # Check for problematic characters in each filter
        filters_to_check = [
            ("Advanced", advanced_filter),
            ("Legacy", legacy_filter), 
            ("Fallback", fallback_filter)
        ]
        
        for name, filter_str in filters_to_check:
            print(f"=== {name.upper()} FILTER ANALYSIS ===")
            print(f"Length: {len(filter_str)}")
            problematic_chars = [':', ';', ',', '(', ')', "'", '"', '=']
            for char in problematic_chars:
                count = filter_str.count(char)
                if count > 0:
                    positions = [i for i, c in enumerate(filter_str) if c == char]
                    print(f"'{char}': {count} times at positions {positions}")
            
            # Check for enable parameter specifically
            if 'enable=' in filter_str:
                enable_start = filter_str.find('enable=')
                enable_part = filter_str[enable_start:enable_start+50]  # Get next 50 chars
                print(f"Enable part: {enable_part}")
            print()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_filter_chain()
