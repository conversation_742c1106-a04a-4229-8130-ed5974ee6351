import React from 'react';
import {
  <PERSON>,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  FormControlLabel,
  Switch,
  Slider,
} from '@mui/material';
import {
  Subtitles as CaptionsIcon,
  Palette as ColorIcon,
  Style as StyleIcon,
  FormatSize as FontSizeIcon,
  FontDownload as FontFamilyIcon,
  WrapText as WordsIcon,
} from '@mui/icons-material';

import { CAPTION_STYLES, CAPTION_COLORS, CAPTION_FONT_FAMILIES } from '../../constants/videoSettings';
import { CAPTION_POSITIONS } from '../../types/contentCreation';

interface CaptionSettingsProps {
  enableCaptions: boolean;
  captionStyle: string;
  captionColor?: string;
  captionPosition?: string;
  fontSize?: number;
  fontFamily?: string;
  wordsPerLine?: number;
  onEnableCaptionsChange: (enabled: boolean) => void; // eslint-disable-line
  onCaptionStyleChange: (style: string) => void; // eslint-disable-line  
  onCaptionColorChange?: (color: string) => void; // eslint-disable-line
  onCaptionPositionChange?: (position: string) => void; // eslint-disable-line
  onFontSizeChange?: (size: number) => void; // eslint-disable-line
  onFontFamilyChange?: (family: string) => void; // eslint-disable-line
  onWordsPerLineChange?: (words: number) => void; // eslint-disable-line
}

const CaptionSettings: React.FC<CaptionSettingsProps> = ({
  enableCaptions,
  captionStyle,
  captionColor,
  captionPosition,
  fontSize,
  fontFamily,
  wordsPerLine,
  onEnableCaptionsChange,
  onCaptionStyleChange,
  onCaptionColorChange,
  onCaptionPositionChange,
  onFontSizeChange,
  onFontFamilyChange,
  onWordsPerLineChange,
}) => {
  const selectedCaptionStyle = CAPTION_STYLES.find(style => style.value === captionStyle);
  const selectedCaptionColor = CAPTION_COLORS.find(color => color.value === captionColor);
  const selectedFontFamily = CAPTION_FONT_FAMILIES.find(font => font.value === fontFamily);

  // Caption style presets - should ideally be handled by backend
  const getCaptionStyleDefaults = (styleValue: string) => {
    const presets: Record<string, { color: string; fontSize: number; fontFamily: string; wordsPerLine: number }> = {
      'viral_bounce': { color: '#FFFF00', fontSize: 72, fontFamily: 'Arial-Bold', wordsPerLine: 4 },
      'viral_cyan': { color: '#00FFFF', fontSize: 68, fontFamily: 'Oswald-VariableFont_wght', wordsPerLine: 3 },
      'viral_yellow': { color: '#FFFF00', fontSize: 70, fontFamily: 'Arial-Bold', wordsPerLine: 4 },
      'viral_green': { color: '#00FF00', fontSize: 68, fontFamily: 'Arial-Bold', wordsPerLine: 4 },
      'bounce': { color: '#FFFFFF', fontSize: 48, fontFamily: 'Arial-Bold', wordsPerLine: 6 },
      'highlight': { color: '#FFFF00', fontSize: 52, fontFamily: 'Arial-Bold', wordsPerLine: 5 },
      'modern_neon': { color: '#00FFFF', fontSize: 64, fontFamily: 'Oswald-VariableFont_wght', wordsPerLine: 3 },
      'cinematic_glow': { color: '#FFFFFF', fontSize: 56, fontFamily: 'DejaVuSans-Bold', wordsPerLine: 5 },
      'social_pop': { color: '#FF1493', fontSize: 60, fontFamily: 'Arial-Bold', wordsPerLine: 4 },
      'classic': { color: '#FFFFFF', fontSize: 24, fontFamily: 'Arial-Regular', wordsPerLine: 8 },
    };
    return presets[styleValue] || { color: '#FFFFFF', fontSize: 48, fontFamily: 'Arial-Bold', wordsPerLine: 6 };
  };

  // Handle caption style change with auto-adjustment using backend presets
  const handleCaptionStyleChange = async (newStyle: string) => {
    onCaptionStyleChange(newStyle);
    
    try {
      // Get API key from localStorage
      const apiKey = localStorage.getItem('apiKey');

      // If no API key, fall back to frontend presets immediately
      if (!apiKey) {
        console.warn('No API key found, using frontend fallback presets');
        const defaults = getCaptionStyleDefaults(newStyle);
        if (onCaptionColorChange) onCaptionColorChange(defaults.color);
        if (onFontSizeChange) onFontSizeChange(defaults.fontSize);
        if (onFontFamilyChange) onFontFamilyChange(defaults.fontFamily);
        if (onWordsPerLineChange) onWordsPerLineChange(defaults.wordsPerLine);
        return;
      }

      // Call backend API to get preset values for this style
      const response = await fetch(`/api/v1/videos/caption-styles/presets/${newStyle}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const preset = data.preset;
        
        // Auto-adjust related settings based on backend preset
        if (onCaptionColorChange && preset.caption_color) {
          onCaptionColorChange(preset.caption_color);
        }
        if (onFontSizeChange && preset.font_size) {
          onFontSizeChange(preset.font_size);
        }
        if (onFontFamilyChange && preset.font_family) {
          onFontFamilyChange(preset.font_family);
        }
        if (onWordsPerLineChange && preset.words_per_line) {
          onWordsPerLineChange(preset.words_per_line);
        }
        if (onCaptionPositionChange && preset.caption_position) {
          onCaptionPositionChange(preset.caption_position);
        }
      } else {
        // Handle different error types
        if (response.status === 401) {
          console.warn(`API key unauthorized for caption presets, using frontend fallback`);
        } else if (response.status === 404) {
          console.warn(`Caption style '${newStyle}' not found on backend, using frontend fallback`);
        } else {
          console.warn(`Failed to fetch preset for style: ${newStyle} (${response.status}), using frontend fallback`);
        }

        // Fallback to frontend presets if backend fails
        const defaults = getCaptionStyleDefaults(newStyle);
        if (onCaptionColorChange) onCaptionColorChange(defaults.color);
        if (onFontSizeChange) onFontSizeChange(defaults.fontSize);
        if (onFontFamilyChange) onFontFamilyChange(defaults.fontFamily);
        if (onWordsPerLineChange) onWordsPerLineChange(defaults.wordsPerLine);
      }
    } catch (error) {
      console.error('Error fetching caption style preset:', error);
      // Fallback to frontend presets if API call fails
      const defaults = getCaptionStyleDefaults(newStyle);
      if (onCaptionColorChange) onCaptionColorChange(defaults.color);
      if (onFontSizeChange) onFontSizeChange(defaults.fontSize);
      if (onFontFamilyChange) onFontFamilyChange(defaults.fontFamily);
      if (onWordsPerLineChange) onWordsPerLineChange(defaults.wordsPerLine);
    }
  };

  const getCaptionStylePreview = (styleValue: string) => {
    const style = CAPTION_STYLES.find(s => s.value === styleValue);
    return style ? `${style.label}` : styleValue;
  };


  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <CaptionsIcon color="primary" />
        Caption Settings
      </Typography>

      <Grid container spacing={3}>
        {/* Enable Captions */}
        <Grid item xs={12} md={4}>
          <FormControlLabel
            control={
              <Switch
                checked={enableCaptions}
                onChange={(e) => onEnableCaptionsChange(e.target.checked)}
              />
            }
            label="Enable Captions"
          />
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
            Add automatic captions to your video
          </Typography>
        </Grid>

        {/* Caption Style */}
        {enableCaptions && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Caption Style</InputLabel>
              <Select
                value={captionStyle}
                onChange={(e) => handleCaptionStyleChange(e.target.value)}
                label="Caption Style"
              >
                {CAPTION_STYLES.map((style) => (
                  <MenuItem key={style.value} value={style.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      <StyleIcon fontSize="small" />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body1">{style.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {style.description}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Caption Color */}
        {enableCaptions && captionColor !== undefined && onCaptionColorChange && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Caption Color</InputLabel>
              <Select
                value={captionColor}
                onChange={(e) => onCaptionColorChange(e.target.value)}
                label="Caption Color"
              >
                {CAPTION_COLORS.map((color) => (
                  <MenuItem key={color.value} value={color.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box
                        sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '50%',
                          backgroundColor: color.value,
                          border: color.value === '#FFFFFF' ? '2px solid #ccc' : '1px solid #444',
                          boxShadow: 1
                        }}
                      />
                      <ColorIcon fontSize="small" />
                      <Box>
                        <Typography variant="body2">{color.label}</Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                          {color.value}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Caption Position */}
        {enableCaptions && captionPosition !== undefined && onCaptionPositionChange && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Caption Position</InputLabel>
              <Select
                value={captionPosition || 'bottom'}
                onChange={(e) => onCaptionPositionChange(e.target.value)}
                label="Caption Position"
              >
                {CAPTION_POSITIONS.map((position) => (
                  <MenuItem key={position} value={position}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {position.charAt(0).toUpperCase() + position.slice(1)}
                      {position === 'bottom' && <Chip label="Recommended" size="small" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Font Size */}
        {enableCaptions && fontSize !== undefined && onFontSizeChange && (
          <Grid item xs={12} md={4}>
            <Typography variant="body2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <FontSizeIcon fontSize="small" color="primary" />
              Font Size: {fontSize}px
            </Typography>
            <Slider
              value={fontSize}
              onChange={(_, value) => onFontSizeChange(Array.isArray(value) ? value[0] : value)}
              min={8}
              max={200}
              step={2}
              marks={[
                { value: 24, label: '24px' },
                { value: 48, label: '48px' },
                { value: 72, label: '72px' },
                { value: 120, label: '120px' }
              ]}
              sx={{ mt: 1 }}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => `${value}px`}
            />
            <Typography variant="caption" color="text.secondary">
              Recommended: 24-72px for mobile, 48-120px for desktop
            </Typography>
          </Grid>
        )}

        {/* Font Family */}
        {enableCaptions && fontFamily !== undefined && onFontFamilyChange && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Font Family</InputLabel>
              <Select
                value={fontFamily || 'Arial-Bold'}
                onChange={(e) => onFontFamilyChange(e.target.value)}
                label="Font Family"
              >
                {CAPTION_FONT_FAMILIES.map((font) => (
                  <MenuItem key={font.value} value={font.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      <FontFamilyIcon fontSize="small" />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2">{font.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {font.category}
                        </Typography>
                      </Box>
                      {font.value === 'Arial-Bold' && <Chip label="Default" size="small" variant="outlined" />}
                      {font.recommended && <Chip label="Recommended" size="small" color="primary" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Words Per Line */}
        {enableCaptions && wordsPerLine !== undefined && onWordsPerLineChange && (
          <Grid item xs={12} md={4}>
            <Typography variant="body2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <WordsIcon fontSize="small" color="primary" />
              Words Per Line: {wordsPerLine}
            </Typography>
            <Slider
              value={wordsPerLine}
              onChange={(_, value) => onWordsPerLineChange(Array.isArray(value) ? value[0] : value)}
              min={1}
              max={20}
              step={1}
              marks={[
                { value: 3, label: '3' },
                { value: 6, label: '6' },
                { value: 10, label: '10' },
                { value: 15, label: '15' }
              ]}
              sx={{ mt: 1 }}
              valueLabelDisplay="auto"
            />
            <Typography variant="caption" color="text.secondary">
              Recommended: 3-6 for mobile, 6-10 for desktop. Lower values improve readability.
            </Typography>
          </Grid>
        )}

        {/* Selected Caption Info */}
        {enableCaptions && selectedCaptionStyle && (
          <Grid item xs={12}>
            <Box sx={{ 
              p: 2, 
              backgroundColor: '#f8f9fa', 
              borderRadius: 1,
              border: '1px solid #e2e8f0'
            }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <StyleIcon />
                {getCaptionStylePreview(selectedCaptionStyle.value)}
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                <Chip 
                  label={selectedCaptionStyle.label} 
                  size="small" 
                  color="primary"
                />
                {selectedCaptionColor && (
                  <Chip 
                    label={selectedCaptionColor.label}
                    size="small" 
                    sx={{
                      backgroundColor: selectedCaptionColor.value,
                      color: selectedCaptionColor.value === '#FFFFFF' || selectedCaptionColor.value === '#FFFF00' ? 'black' : 'white',
                      border: selectedCaptionColor.value === '#FFFFFF' ? '1px solid #ccc' : 'none'
                    }}
                  />
                )}
                {captionPosition && (
                  <Chip 
                    label={`${captionPosition.charAt(0).toUpperCase() + captionPosition.slice(1)} Position`}
                    size="small" 
                    variant="outlined"
                  />
                )}
                {fontSize && (
                  <Chip 
                    label={`${fontSize}px`}
                    size="small" 
                    variant="outlined"
                  />
                )}
                {selectedFontFamily && (
                  <Chip 
                    label={selectedFontFamily.label}
                    size="small" 
                    variant="outlined"
                  />
                )}
                {wordsPerLine && (
                  <Chip 
                    label={`${wordsPerLine} words/line`}
                    size="small" 
                    variant="outlined"
                  />
                )}
                <Chip 
                  label="Auto-Generated" 
                  size="small" 
                  variant="outlined"
                />
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                Captions will be automatically generated from your video's audio and styled with the selected appearance settings.
              </Typography>
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Caption Information */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          📝 <strong>Available:</strong> {CAPTION_STYLES.length} caption styles, {CAPTION_COLORS.length} colors, {CAPTION_FONT_FAMILIES.length} fonts
          • Current: {enableCaptions ? `${selectedCaptionStyle?.label || 'Default'} style` : 'Disabled'}
          {selectedCaptionColor && ` in ${selectedCaptionColor.label}`}
          {captionPosition && ` at ${captionPosition} position`}
          {fontSize && ` • ${fontSize}px`}
          {selectedFontFamily && ` ${selectedFontFamily.label}`}
          {wordsPerLine && ` • ${wordsPerLine} words/line`}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          💡 <strong>Note:</strong> "Viral Bounce" is the most effective style for engagement. Bold fonts (Arial, Helvetica, Roboto) are recommended for readability.
        </Typography>
      </Box>
    </Box>
  );
};

export default CaptionSettings;