import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  AppBar,
  Box,
  CssBaseline,
  Drawer,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Button,
  ThemeProvider,
  createTheme,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
  Collapse,
  Tooltip,
  Badge
} from '@mui/material';
import {
  VideoLibrary,
  List as ListIcon,
  Logout,
  Person,
  Settings,
  MenuBook,
  Dashboard as DashboardIcon,
  People as UsersIcon,
  Work as JobsIcon,
  VpnKey as ApiKeyIcon,
  Menu as MenuIcon,
  ChevronLeft,
  ExpandLess,
  ExpandMore,
  AdminPanelSettings,
  VolumeUp as AudioIcon,
  Code as CodeIcon,
  Build as BuildIcon,
  Description as DocumentIcon,
  Image as ImageIcon,
  Storage as MediaIcon,
  CloudUpload as UploadIcon,
  SmartToy as <PERSON><PERSON><PERSON>,
  YouTube as YouTubeIcon,
  Movie as VideoToolsIcon,
  AutoAwesome as <PERSON>VideoIcon,
  Psychology as AIIcon,
  Feedback as FeedbackIcon
} from '@mui/icons-material';

interface LayoutProps {
  children: React.ReactNode;
}

const DRAWER_WIDTH = 280;
const DRAWER_WIDTH_COLLAPSED = 64;

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#f50057',
      light: '#ff5983',
      dark: '#c51162',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
    grey: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#eeeeee',
      300: '#e0e0e0',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
    },
    h5: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
          backgroundColor: '#ffffff',
          color: '#1a202c',
          borderBottom: '1px solid #e2e8f0',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          borderRight: '1px solid #e2e8f0',
          backgroundColor: '#ffffff',
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          margin: '2px 8px',
          transition: 'all 0.2s ease-in-out',
          '&.Mui-selected': {
            // Default active state (bright blue for child pages)
            backgroundColor: '#3b82f6',
            color: '#ffffff',
            '& .MuiListItemIcon-root': {
              color: '#ffffff',
            },
            '&:hover': {
              backgroundColor: '#2563eb',
            },
          },
          '&.parent-active': {
            // Parent active state (subtle blue for dashboard when child is active)
            backgroundColor: '#e0f2fe',
            color: '#0369a1',
            borderLeft: '3px solid #0ea5e9',
            borderTopRightRadius: 8,
            borderBottomRightRadius: 8,
            '& .MuiListItemIcon-root': {
              color: '#0369a1',
            },
            '&:hover': {
              backgroundColor: '#b3e5fc',
            },
          },
          '&:hover': {
            backgroundColor: '#f1f5f9',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
        },
      },
    },
  },
});

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  children?: NavItem[];
  badge?: number;
  adminOnly?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, apiKey, userRole } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [expandedItems, setExpandedItems] = React.useState<string[]>(['video-content']);

  const navigationItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/dashboard'
    },
    {
      id: 'content',
      label: 'Content Creation',
      icon: <VideoLibrary />,
      path: '/dashboard/content-creation'
    },
    {
      id: 'video-content',
      label: 'Video Tools',
      icon: <VideoToolsIcon />,
      children: [
        {
          id: 'simone',
          label: 'Simone AI',
          icon: <SimoneIcon />,
          path: '/dashboard/simone'
        },
        {
          id: 'yt-shorts',
          label: 'YouTube Shorts',
          icon: <YouTubeIcon />,
          path: '/dashboard/yt-shorts'
        },
        {
          id: 'video-tools',
          label: 'Video Tools',
          icon: <VideoToolsIcon />,
          path: '/dashboard/video-tools'
        },
        {
          id: 'ai-video-tools',
          label: 'AI Script & Search Tools',
          icon: <AIVideoIcon />,
          path: '/dashboard/ai-video-tools'
        }
      ]
    },
    {
      id: 'tools',
      label: 'Tools',
      icon: <BuildIcon />,
      children: [
        {
          id: 'audio',
          label: 'Audio Tools',
          icon: <AudioIcon />,
          path: '/dashboard/audio'
        },
        {
          id: 'images',
          label: 'Image Tools',
          icon: <ImageIcon />,
          path: '/dashboard/images'
        },
        {
          id: 'pollinations',
          label: 'Pollinations AI',
          icon: <AIIcon />,
          path: '/dashboard/pollinations'
        },
        {
          id: 'code',
          label: 'Code Executor',
          icon: <CodeIcon />,
          path: '/dashboard/code'
        },
        {
          id: 'documents',
          label: 'Documents',
          icon: <DocumentIcon />,
          path: '/dashboard/documents'
        },
        {
          id: 'media',
          label: 'Media Tools',
          icon: <MediaIcon />,
          path: '/dashboard/media'
        },
        {
          id: 'upload',
          label: 'File Upload',
          icon: <UploadIcon />,
          path: '/dashboard/upload'
        },
        {
          id: 'feedback',
          label: 'Feedback Magic',
          icon: <FeedbackIcon />,
          path: '/dashboard/feedback'
        }
      ]
    },
    {
      id: 'library',
      label: 'Library',
      icon: <ListIcon />,
      path: '/dashboard/library'
    },
    {
      id: 'management',
      label: 'Management',
      icon: <AdminPanelSettings />,
      children: [
        {
          id: 'jobs',
          label: 'Jobs & Cleanup',
          icon: <JobsIcon />,
          path: '/dashboard/jobs',
          badge: 3
        },
        {
          id: 'users',
          label: 'Users',
          icon: <UsersIcon />,
          path: '/dashboard/admin/users',
          adminOnly: true
        },
        {
          id: 'api-keys',
          label: 'API Keys',
          icon: <ApiKeyIcon />,
          path: '/dashboard/admin/api-keys',
          adminOnly: true
        }
      ]
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings />,
      path: '/dashboard/settings'
    }
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
    handleProfileMenuClose();
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleExpandToggle = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (path: string) => {
    // Exact match for dashboard to avoid conflicts
    if (path === '/dashboard' && location.pathname === '/dashboard') return true;
    // For other paths, check if current path starts with the nav path
    if (path !== '/dashboard' && location.pathname.startsWith(path)) return true;
    return false;
  };

  const isParentActive = (item: NavItem) => {
    // Check if any child is active (for parent highlighting)
    if (!item.children) return false;
    return item.children.some(child => child.path && isActive(child.path));
  };

  const maskedApiKey = apiKey ? `${apiKey.substring(0, 8)}...` : '';

  const filterNavItems = (items: NavItem[]): NavItem[] => {
    return items.filter(item => {
      // Filter out admin-only items for non-admin users
      if (item.adminOnly && userRole !== 'admin') {
        return false;
      }
      
      // Recursively filter children
      if (item.children) {
        const filteredChildren = filterNavItems(item.children);
        // If a parent has children but all are filtered out, hide the parent too
        if (filteredChildren.length === 0) {
          return false;
        }
        // Update the item with filtered children
        item.children = filteredChildren;
      }
      
      return true;
    });
  };

  const filteredNavigationItems = filterNavItems([...navigationItems]);

  const renderNavItem = (item: NavItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = item.path ? isActive(item.path) : false;
    const parentActiveState = hasChildren ? isParentActive(item) : false;

    if (hasChildren) {
      return (
        <React.Fragment key={item.id}>
          <ListItem disablePadding>
            <ListItemButton
              onClick={() => handleExpandToggle(item.id)}
              className={parentActiveState ? 'parent-active' : ''}
              sx={{ 
                pl: level * 2 + 2,
                minHeight: 48
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              {!sidebarCollapsed && (
                <>
                  <ListItemText 
                    primary={item.label}
                    primaryTypographyProps={{
                      fontSize: '0.875rem',
                      fontWeight: parentActiveState ? 600 : 500
                    }}
                  />
                  {isExpanded ? <ExpandLess /> : <ExpandMore />}
                </>
              )}
            </ListItemButton>
          </ListItem>
          {!sidebarCollapsed && (
            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {item.children?.map(child => renderNavItem(child, level + 1))}
              </List>
            </Collapse>
          )}
        </React.Fragment>
      );
    }

    // Special handling for Dashboard to show parent active state when on sub-pages
    const isDashboard = item.id === 'dashboard';
    const dashboardParentActive = isDashboard && location.pathname !== '/dashboard' && location.pathname.startsWith('/dashboard');

    return (
      <ListItem key={item.id} disablePadding>
        <Tooltip 
          title={sidebarCollapsed ? item.label : ''} 
          placement="right"
          arrow
        >
          <ListItemButton
            selected={active}
            className={dashboardParentActive ? 'parent-active' : ''}
            onClick={() => item.path && handleNavigation(item.path)}
            sx={{ 
              pl: level * 2 + 2,
              minHeight: 48
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              {item.badge ? (
                <Badge badgeContent={item.badge} color="error">
                  {item.icon}
                </Badge>
              ) : (
                item.icon
              )}
            </ListItemIcon>
            {!sidebarCollapsed && (
              <ListItemText 
                primary={item.label}
                primaryTypographyProps={{
                  fontSize: '0.875rem',
                  fontWeight: (active || dashboardParentActive) ? 600 : 500
                }}
              />
            )}
          </ListItemButton>
        </Tooltip>
      </ListItem>
    );
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo Section */}
      <Box sx={{ 
        p: sidebarCollapsed ? 1 : 2, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
        borderBottom: '1px solid #e2e8f0',
        minHeight: 64
      }}>
        <VideoLibrary 
          sx={{ 
            color: theme.palette.primary.main,
            fontSize: 32
          }} 
        />
        {!sidebarCollapsed && (
          <Typography 
            variant="h6" 
            sx={{ 
              ml: 1,
              fontWeight: 700,
              color: theme.palette.primary.main,
              letterSpacing: '-0.025em'
            }}
          >
            Ouinhi
          </Typography>
        )}
      </Box>

      {/* Navigation */}
      <Box sx={{ flexGrow: 1, py: 1 }}>
        <List>
          {filteredNavigationItems.map(item => renderNavItem(item))}
        </List>
      </Box>

      {/* Collapse Button */}
      {!isMobile && (
        <Box sx={{ p: 1, borderTop: '1px solid #e2e8f0' }}>
          <Tooltip title={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'} placement="right">
            <IconButton
              onClick={handleSidebarToggle}
              sx={{ 
                width: '100%',
                borderRadius: 2,
                '&:hover': {
                  backgroundColor: '#f1f5f9'
                }
              }}
            >
              {sidebarCollapsed ? <MenuIcon /> : <ChevronLeft />}
            </IconButton>
          </Tooltip>
        </Box>
      )}
    </Box>
  );

  const drawerWidth = sidebarCollapsed ? DRAWER_WIDTH_COLLAPSED : DRAWER_WIDTH;

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* App Bar */}
        <AppBar
          position="fixed"
          sx={{
            width: { lg: `calc(100% - ${drawerWidth}px)` },
            ml: { lg: `${drawerWidth}px` },
            zIndex: theme.zIndex.drawer + 1,
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2, display: { lg: 'none' } }}
              >
                <MenuIcon />
              </IconButton>
              
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a202c' }}>
                {location.pathname === '/' ? 'Dashboard' : 
                 navigationItems.flatMap(item => [item, ...(item.children || [])])
                   .find(item => item.path === location.pathname)?.label || 'Ouinhi'}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<MenuBook />}
                onClick={() => window.open('/docs', '_blank')}
                sx={{ 
                  display: { xs: 'none', md: 'flex' },
                  borderColor: '#e2e8f0',
                  color: '#64748b',
                  '&:hover': {
                    borderColor: '#cbd5e1',
                    backgroundColor: '#f8fafc'
                  }
                }}
              >
                Docs
              </Button>

              <IconButton
                onClick={handleProfileMenuOpen}
                size="small"
                sx={{ 
                  '&:hover': {
                    backgroundColor: '#f1f5f9'
                  }
                }}
              >
                <Avatar sx={{ 
                  width: 36, 
                  height: 36, 
                  backgroundColor: theme.palette.primary.main,
                  fontSize: '0.875rem',
                  fontWeight: 600
                }}>
                  <Person />
                </Avatar>
              </IconButton>

              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleProfileMenuClose}
                onClick={handleProfileMenuClose}
                slotProps={{
                  paper: {
                    elevation: 8,
                    sx: {
                      overflow: 'visible',
                      filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.15))',
                      mt: 1.5,
                      minWidth: 240,
                      borderRadius: 2,
                      border: '1px solid #e2e8f0',
                      '& .MuiAvatar-root': {
                        width: 32,
                        height: 32,
                        ml: -0.5,
                        mr: 1,
                      },
                    },
                  }
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
              >
                <MenuItem disabled sx={{ opacity: 1 }}>
                  <Avatar sx={{ backgroundColor: theme.palette.primary.main }}>
                    <Person />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {userRole === 'admin' ? 'Administrator' : 'User'}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {maskedApiKey}
                    </Typography>
                  </Box>
                </MenuItem>
                
                <Divider />
                
                <MenuItem onClick={() => window.open('/docs', '_blank')}>
                  <MenuBook sx={{ mr: 2, fontSize: 20 }} />
                  Documentation
                </MenuItem>
                
                
                <MenuItem onClick={handleLogout}>
                  <Logout sx={{ mr: 2, fontSize: 20 }} />
                  Logout
                </MenuItem>
              </Menu>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Sidebar */}
        <Box
          component="nav"
          sx={{ 
            width: { lg: drawerWidth }, 
            flexShrink: { lg: 0 }
          }}
        >
          {/* Mobile drawer */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: 'block', lg: 'none' },
              '& .MuiDrawer-paper': { 
                boxSizing: 'border-box', 
                width: DRAWER_WIDTH 
              },
            }}
          >
            {drawer}
          </Drawer>

          {/* Desktop drawer */}
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', lg: 'block' },
              '& .MuiDrawer-paper': { 
                boxSizing: 'border-box', 
                width: drawerWidth,
                transition: theme.transitions.create('width', {
                  easing: theme.transitions.easing.sharp,
                  duration: theme.transitions.duration.enteringScreen,
                }),
              },
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        {/* Main content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            width: { lg: `calc(100% - ${drawerWidth}px)` },
            minHeight: '100vh',
            backgroundColor: '#f8fafc',
          }}
        >
          <Toolbar />
          <Box sx={{ 
            p: { xs: 2, sm: 3 },
            height: 'calc(100vh - 64px)',
            overflow: 'auto'
          }}>
            {children}
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default Layout;
