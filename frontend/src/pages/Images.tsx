import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  Tab,
  Tabs,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Edit as EditIcon,
  AutoAwesome as GenerateIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Preview as PreviewIcon,
  Download as DownloadIcon,
  ExpandMore as ExpandMoreIcon,
  VideoLibrary as LibraryIcon,
  PhotoCamera as FluxIcon,
  Upload as UploadIcon,
  Search as SearchIcon,
  AutoFixHigh as EnhanceIcon
} from '@mui/icons-material';
import { directApi, pollinationsApi } from '../utils/api';
import ImageProviderSettings from '../components/settings/ImageProviderSettings';

// Type definitions
interface ImageGenerationParams extends Record<string, unknown> {
  prompt: string;
  model?: string;
  width?: number;
  height?: number;
  steps?: number;
  provider?: string;
  seed?: number;
}

interface OverlayImage {
  url: string;
  x: number;
  y: number;
  width?: number;
  height?: number;
  rotation?: number;
  opacity?: number;
  z_index?: number;
}

interface ImageEditParams extends Record<string, unknown> {
  base_image_url: string;
  overlay_images: OverlayImage[];
  output_format?: string;
  output_quality?: number;
  output_width?: number;
  output_height?: number;
  maintain_aspect_ratio?: boolean;
  // Image Stitching Settings
  stitch_mode?: boolean;
  stitch_direction?: string;
  stitch_spacing?: number;
  stitch_max_width?: number;
  stitch_max_height?: number;
}

interface FluxEditParams {
  prompt: string;
  guidance_scale?: number;
  num_inference_steps?: number;
  seed?: number;
}

interface ImageEnhancementParams extends Record<string, unknown> {
  image_url: string;
  enhance_color?: number;
  enhance_contrast?: number;
  noise_strength?: number;
  remove_artifacts?: boolean;
  add_film_grain?: boolean;
  vintage_effect?: number;
  output_format?: string;
  output_quality?: number;
}

// Job result interface
interface JobResult {
  job_id: string;
  status: string;
  result?: {
    image_url?: string;
    content_url?: string;
    original_image_url?: string;
    edited_image_url?: string;
    prompt_used?: string;
    model_used?: string;
    dimensions?: { width: number; height: number };
    processing_time?: number;
    width?: number;
    height?: number;
    format?: string;
    storage_path?: string;
    content_type?: string;
    file_size?: number;
    generation_time?: number;
    // Image search results
    images?: Array<{
      id: string;
      url: string;
      download_url: string;
      width: number;
      height: number;
      photographer?: string;
      photographer_url?: string;
      alt?: string;
      tags?: string;
      source: string;
      aspect_ratio: number;
    }>;
    total_results?: number;
    page?: number;
    per_page?: number;
    query_used?: string;
    provider_used?: string;
  };
  error?: string | null;
}

const Images: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<JobResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [jobProgress, setJobProgress] = useState<string>('');
  const [pollingJobId, setPollingJobId] = useState<string | null>(null);
  const [previewDialog, setPreviewDialog] = useState(false);

  // Component lifecycle tracking
  const isMountedRef = useRef(true);
  const lastApiCallTimeRef = useRef(0);
  const modelsCache = useRef<{ models: string[]; timestamp: number } | null>(null);
  const pollinationsCallsBlockedRef = useRef(0); // Track blocked calls for debugging

  // Dynamic model states
  const [imageModels, setImageModels] = useState<string[]>([]);
  const [loadingModels, setLoadingModels] = useState(true);
  const [modelError, setModelError] = useState<string | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Image Search state
  const [searchForm, setSearchForm] = useState({
    query: '',
    provider: 'pexels',
    limit: 20,
    category: 'all',
    orientation: 'all',
    size: 'all'
  });
  const [searchResults, setSearchResults] = useState<Array<{
    id: string;
    url: string;
    download_url: string;
    width: number;
    height: number;
    photographer?: string;
    photographer_url?: string;
    alt?: string;
    tags?: string;
    source: string;
    aspect_ratio: number;
  }> | null>(null);

  // Image Generation state
  const [generateForm, setGenerateForm] = useState<ImageGenerationParams>({
    prompt: '',
    model: 'flux-kontext-dev', // Default to flux-kontext-dev for Flux provider
    width: 576,
    height: 1024,
    steps: 4,
    provider: 'flux' // Default to flux instead of pollinations to avoid unnecessary API calls
  });

  // Ensure generateForm provider is always an AI provider (not stock)
  useEffect(() => {
    if (generateForm.provider === 'pexels' || generateForm.provider === 'pixabay') {
      setGenerateForm(prev => ({
        ...prev,
        provider: 'flux' // Default to flux instead of pollinations to avoid unnecessary API calls
      }));
    }
  }, [generateForm.provider]);

  // Load dynamic image models ONLY for Pollinations provider with aggressive caching
  useEffect(() => {
    const loadImageModels = async () => {
      // STRICT CHECK: Only proceed if explicitly using Pollinations AND on generation tab
      if (generateForm.provider !== 'pollinations' || activeTab !== 0) {
        pollinationsCallsBlockedRef.current += 1;
        setLoadingModels(false);
        setImageModels([]); // Clear any existing models
        return;
      }

      // Check cache first (10 minute cache)
      const now = Date.now();
      const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
      
      if (modelsCache.current && (now - modelsCache.current.timestamp) < CACHE_DURATION) {
        setImageModels(modelsCache.current.models);
        setLoadingModels(false);
        setModelError(null);
        return;
      }

      // Check if component is still mounted
      if (!isMountedRef.current) {
        return;
      }

      // Aggressive debouncing - prevent calls within 30 seconds of each other
      if (now - lastApiCallTimeRef.current < 30000) {
        setLoadingModels(false);
        return;
      }
      lastApiCallTimeRef.current = now;
      
      try {
        setLoadingModels(true);
        setModelError(null);

        // Add delay to prevent flooding API
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Double-check component is still mounted and provider hasn't changed
        if (!isMountedRef.current || generateForm.provider !== 'pollinations') {
          return;
        }

        // Fetch image models from Pollinations
        const imageModelsResponse = await pollinationsApi.listImageModels();
        
        // Final check before setting state
        if (!isMountedRef.current) {
          return;
        }

        if (imageModelsResponse.success && imageModelsResponse.data && imageModelsResponse.data.models) {
          const models = imageModelsResponse.data.models as string[];
          setImageModels(models);
          
          // Cache the results
          modelsCache.current = {
            models: models,
            timestamp: now
          };
        } else {
          throw new Error(imageModelsResponse.error || 'Failed to fetch image models');
        }
      } catch (err) {
        if (!isMountedRef.current) {
          return;
        }
        
        const errorMessage = err instanceof Error ? err.message : 'Failed to load models';
        setModelError(errorMessage);
        
        // Use fallback models (matching backend fallbacks)
        const fallbackModels = ['flux', 'flux-realism', 'flux-cablyai', 'flux-anime', 'any-dark'];
        setImageModels(fallbackModels);
        
        // Cache fallback models too (shorter duration)
        modelsCache.current = {
          models: fallbackModels,
          timestamp: now - (CACHE_DURATION - 60000) // Cache for 1 minute only
        };
      } finally {
        if (isMountedRef.current) {
          setLoadingModels(false);
        }
      }
    };

    // Only run if we're actually using the component and focused
    // Remove document.hasFocus() check as it can cause unnecessary calls
    if (isMountedRef.current) {
      loadImageModels();
    } else {
      setLoadingModels(false);
    }
  }, [generateForm.provider, activeTab]);

  // Update model when provider changes (with dynamic models)
  React.useEffect(() => {
    if (generateForm.provider === 'pollinations' && imageModels.length > 0) {
      // Use dynamic models for Pollinations
      setGenerateForm(prev => {
        // Only update if current model is not in the available models
        if (!imageModels.includes(prev.model || '')) {
          return { ...prev, model: imageModels[0] };
        }
        return prev;
      });
    } else if (generateForm.provider === 'flux') {
      setGenerateForm(prev => ({ ...prev, model: 'flux-kontext-dev' }));
    } else if (generateForm.provider === 'together') {
      setGenerateForm(prev => ({ ...prev, model: 'black-forest-labs/FLUX.1-schnell-Free' }));
    }
  }, [generateForm.provider, imageModels]); // Intentionally excluding generateForm.model to prevent infinite loops

  // Process search results when result changes
  useEffect(() => {
    if (result && result.status === 'completed' && result.result) {
      // Check if the result contains images (search result)
      if (result.result.images && Array.isArray(result.result.images)) {
        setSearchResults(result.result.images);
      }
    }
  }, [result]);

  // Image Editing state
  const [editForm, setEditForm] = useState<ImageEditParams>({
    base_image_url: '',
    overlay_images: [],
    output_format: 'png',
    output_quality: 90,
    maintain_aspect_ratio: true,
    stitch_mode: false,
    stitch_direction: 'horizontal',
    stitch_spacing: 0,
    stitch_max_width: 1920,
    stitch_max_height: 1080
  });

  // Flux Image Editing state
  const [fluxEditForm, setFluxEditForm] = useState<FluxEditParams>({
    prompt: '',
    guidance_scale: 3.5,
    num_inference_steps: 20,
    seed: undefined
  });

  // Image Enhancement state
  const [enhanceForm, setEnhanceForm] = useState<ImageEnhancementParams>({
    image_url: '',
    enhance_color: 1.0,
    enhance_contrast: 1.0,
    noise_strength: 10,
    remove_artifacts: true,
    add_film_grain: false,
    vintage_effect: 0.0,
    output_format: 'png',
    output_quality: 90
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Handle file upload
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }
      
      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        setError('File size must be less than 10MB');
        return;
      }
      
      setSelectedFile(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      setError(null);
    }
  };

  // Job status polling function for image search
  const pollImageSearchStatus = async (jobId: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.getImageSearchStatus(jobId);

        if (!statusResponse.success) {
          throw new Error(statusResponse.error || 'Failed to get image search status');
        }

        const status = statusResponse.data?.status;
        const jobResult = statusResponse.data?.result;
        const jobError = statusResponse.data?.error;

        setJobStatus(status as 'pending' | 'processing' | 'completed' | 'failed');

        if (status === 'completed') {
          setJobProgress('Image search completed successfully!');
          setResult({ job_id: jobId, result: jobResult, status: 'completed' });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Image search failed');
          setError(jobError || 'Image search failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Searching images... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 3000); // Poll every 3 seconds for search
        } else {
          setError('Image search timeout. Please try again.');
          setLoading(false);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to poll image search status');
        setLoading(false);
      }
    };

    poll();
  };

  // Job status polling function
  const pollJobStatus = async (jobId: string, endpoint: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`/api/v1/images/${endpoint === 'edit' ? 'edit_image' : 'generate'}/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatus(status);

        if (status === 'completed') {
          setJobProgress('Image processing completed successfully!');
          setResult({ job_id: jobId, result: jobResult, status: 'completed' });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Image processing failed');
          setError(jobError || 'Image processing failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  // Job status polling function for Pollinations
  const pollJobStatusPollinations = async (jobId: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await pollinationsApi.getImageGenerationStatus(jobId);

        if (!statusResponse.success || !statusResponse.data) {
          throw new Error(statusResponse.error || 'Failed to get job status');
        }

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatus(status as 'pending' | 'processing' | 'completed' | 'failed');

        if (status === 'completed') {
          setJobProgress('Image generation completed successfully!');
          setResult({ 
            job_id: jobId, 
            result: {
              image_url: jobResult?.content_url,
              content_url: jobResult?.content_url,
              content_type: jobResult?.content_type,
              file_size: jobResult?.file_size,
              generation_time: jobResult?.generation_time,
              model_used: jobResult?.model_used,
              prompt_used: jobResult?.prompt,
              dimensions: { width: generateForm.width || 1024, height: generateForm.height || 1024 },
              width: generateForm.width,
              height: generateForm.height
            }, 
            status: 'completed' 
          });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Image generation failed');
          setError(jobError || 'Image generation failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing with Pollinations... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check Pollinations job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  const handleGenerateSubmit = async () => {
    if (!generateForm.prompt.trim()) {
      setError('Prompt is required for image generation');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      let response;
      
      if (generateForm.provider === 'pollinations') {
        // Use Pollinations API for AI generation
        const pollinationsResponse = await pollinationsApi.generateImage({
          prompt: generateForm.prompt,
          model: generateForm.model,
          width: generateForm.width,
          height: generateForm.height,
          seed: generateForm.seed,
          enhance: false,
          nologo: true,
          safe: false,
          transparent: false
        });
        
        if (pollinationsResponse.success) {
          response = { data: pollinationsResponse.data };
        } else {
          throw new Error(pollinationsResponse.error || 'Failed to generate image with Pollinations');
        }
      } else {
        // Use AI generation for Together, Flux, etc.
        response = await directApi.post('/api/v1/images/generate', generateForm);
      }

      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting image generation...');
        
        if (generateForm.provider === 'pollinations') {
          pollJobStatusPollinations(response.data.job_id);
        } else {
          pollJobStatus(response.data.job_id, 'generate');
        }
      } else {
        setError('Failed to create image generation job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading(false);
    }
  };

  // Image search submit handler
  const handleSearchSubmit = async () => {
    if (!searchForm.query.trim()) {
      setError('Search query is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);
    setSearchResults(null);

    try {
      const response = await directApi.searchStockImages({
        query: searchForm.query,
        provider: searchForm.provider as 'pexels' | 'pixabay',
        per_page: searchForm.limit,
        orientation: searchForm.orientation === 'all' ? undefined : 
          (searchForm.orientation === 'horizontal' ? 'landscape' : 
           searchForm.orientation === 'vertical' ? 'portrait' : 
           searchForm.orientation as 'landscape' | 'portrait' | 'square'),
        size: searchForm.size === 'all' ? undefined : searchForm.size as 'large' | 'medium' | 'small'
      });

      if (response.data && response.data.job_id) {
        setResult({ job_id: response.data.job_id, status: 'pending', result: undefined });
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Searching for stock images...');
        
        pollImageSearchStatus(response.data.job_id);
      } else {
        setError('Failed to create image search job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading(false);
    }
  };

  const handleEditSubmit = async () => {
    if (!editForm.base_image_url.trim()) {
      setError('Base image URL is required');
      return;
    }

    if (editForm.overlay_images.length === 0) {
      setError('At least one overlay image is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await directApi.post('/api/v1/images/edit', editForm);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting image editing...');
        pollJobStatus(response.data.job_id, 'edit');
      } else {
        setError('Failed to create image editing job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading(false);
    }
  };

  const handleFluxEditSubmit = async () => {
    if (!selectedFile) {
      setError('Please select an image file to edit');
      return;
    }

    if (!fluxEditForm.prompt.trim()) {
      setError('Edit prompt is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('prompt', fluxEditForm.prompt);
      formData.append('guidance_scale', fluxEditForm.guidance_scale?.toString() || '3.5');
      formData.append('num_inference_steps', fluxEditForm.num_inference_steps?.toString() || '20');
      
      if (fluxEditForm.seed !== undefined && fluxEditForm.seed !== null) {
        formData.append('seed', fluxEditForm.seed.toString());
      }
      

      const response = await directApi.post('/api/v1/images/edit_image', formData);

      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting AI image editing...');
        pollJobStatus(response.data.job_id, 'edit');
      } else {
        setError('Failed to create Flux image editing job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading(false);
    }
  };

  const handleEnhancementSubmit = async () => {
    if (!enhanceForm.image_url.trim()) {
      setError('Image URL is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await directApi.post('/api/v1/images/enhance', enhanceForm);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting image enhancement...');
        pollJobStatus(response.data.job_id, 'enhance');
      } else {
        setError('Failed to create image enhancement job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading(false);
    }
  };

  const addOverlayImage = () => {
    setEditForm(prev => ({
      ...prev,
      overlay_images: [
        ...prev.overlay_images,
        {
          url: '',
          x: 0.5,
          y: 0.5,
          width: 0.2,
          height: 0.2,
          rotation: 0,
          opacity: 1.0,
          z_index: prev.overlay_images.length
        }
      ]
    }));
  };

  const removeOverlayImage = (index: number) => {
    setEditForm(prev => ({
      ...prev,
      overlay_images: prev.overlay_images.filter((_, i) => i !== index)
    }));
  };

  const updateOverlayImage = (index: number, field: keyof OverlayImage, value: string | number | number[]) => {
    setEditForm(prev => ({
      ...prev,
      overlay_images: prev.overlay_images.map((overlay, i) => 
        i === index ? { ...overlay, [field]: value } : overlay
      )
    }));
  };

  const presetDimensions = [
    { label: 'Portrait (9:16)', width: 576, height: 1024 },
    { label: 'Landscape (16:9)', width: 1024, height: 576 },
    { label: 'Square (1:1)', width: 768, height: 768 },
    { label: 'Widescreen (16:9 HD)', width: 1280, height: 720 },
    { label: 'Ultra-wide Banner', width: 1536, height: 512 }
  ];

  const examplePrompts = [
    'A majestic mountain landscape at sunset with golden light reflecting on a crystal clear lake',
    'Professional portrait of a young woman with curly brown hair, soft natural lighting, studio photography',
    'Modern minimalist workspace with laptop, coffee cup, and plants, clean aesthetic',
    'Futuristic cityscape at night, neon lights, cyberpunk style, detailed digital art',
    'Luxury watch on marble surface, dramatic lighting, commercial product photography'
  ];

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          AI Image Tools 🎨
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Generate stunning images from text prompts or edit existing images with precise overlay controls.
        </Typography>
      </Box>

      {/* Tab Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_e, newValue) => setActiveTab(newValue)}>
          <Tab 
            icon={<GenerateIcon />} 
            label="Generate Images" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
          <Tab 
            icon={<EditIcon />} 
            label="Overlay Editing" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
          <Tab 
            icon={<FluxIcon />} 
            label="AI Image Editing" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
          <Tab 
            icon={<EnhanceIcon />} 
            label="Image Enhancement" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
          <Tab 
            icon={<SearchIcon />} 
            label="Search Stock Images" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
        </Tabs>
      </Box>

      {/* Image Generation Tab */}
      {activeTab === 0 && (
        <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <GenerateIcon color="primary" />
                      AI Image Generation
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={4}
                          label="Image Description Prompt"
                          placeholder="Describe the image you want to create..."
                          value={generateForm.prompt}
                          onChange={(e) => setGenerateForm({ ...generateForm, prompt: e.target.value })}
                          helperText="Be descriptive and specific for better results. Include style, lighting, and composition details."
                        />
                      </Grid>

                      {/* Image Provider Settings */}
                    </Grid>
                    
                    <ImageProviderSettings
                      imageProvider={generateForm.provider || 'flux'}
                      searchSafety="moderate"
                      guidanceScale={3.5}
                      inferenceSteps={generateForm.steps}
                      showOnlyAiProviders={true} // Show only AI providers for generation
                      onImageProviderChange={(value) => {
                        setGenerateForm({ ...generateForm, provider: value });
                      }}
                      onSearchSafetyChange={() => {}} // Not used in generation
                      onGuidanceScaleChange={() => {}} // Not used in generation, only in editing
                      onInferenceStepsChange={(value) => setGenerateForm({ ...generateForm, steps: value })}
                      showAdvancedSettings={true}
                    />
                    
                    {/* Dynamic Model Selection for Pollinations */}
                    {generateForm.provider === 'pollinations' && (
                      <Grid container spacing={3} sx={{ mt: 1 }}>
                        <Grid item xs={12} md={6}>
                          <FormControl fullWidth>
                            <InputLabel>AI Model</InputLabel>
                            <Select
                              value={generateForm.model || ''}
                              label="AI Model"
                              onChange={(e) => setGenerateForm({ ...generateForm, model: e.target.value })}
                              disabled={loadingModels}
                            >
                              {loadingModels ? (
                                <MenuItem disabled>Loading models...</MenuItem>
                              ) : imageModels.length > 0 ? (
                                imageModels.map((model) => (
                                  <MenuItem key={model} value={model}>
                                    {model.charAt(0).toUpperCase() + model.slice(1).replace(/-/g, ' ')}
                                  </MenuItem>
                                ))
                              ) : (
                                <MenuItem disabled>No models available</MenuItem>
                              )}
                            </Select>
                            {modelError && (
                              <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                                {modelError}
                              </Typography>
                            )}
                          </FormControl>
                        </Grid>
                      </Grid>
                    )}
                    
                    <Grid container spacing={3} sx={{ mt: 1 }}>

                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Preset Dimensions</InputLabel>
                          <Select
                            value={`${generateForm.width}x${generateForm.height}`}
                            label="Preset Dimensions"
                            onChange={(e) => {
                              const [width, height] = e.target.value.split('x').map(Number);
                              setGenerateForm({ 
                                ...generateForm, 
                                width: width, 
                                height: height 
                              });
                            }}
                          >
                            {presetDimensions.map((preset) => (
                              <MenuItem key={preset.label} value={`${preset.width}x${preset.height}`}>
                                {preset.label} ({preset.width}×{preset.height})
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={6} md={3}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Width"
                          value={generateForm.width}
                          onChange={(e) => setGenerateForm({ ...generateForm, width: parseInt(e.target.value) })}
                          inputProps={{ min: 256, max: 2048, step: 64 }}
                        />
                      </Grid>

                      <Grid item xs={6} md={3}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Height"
                          value={generateForm.height}
                          onChange={(e) => setGenerateForm({ ...generateForm, height: parseInt(e.target.value) })}
                          inputProps={{ min: 256, max: 2048, step: 64 }}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Typography gutterBottom>Quality Steps: {generateForm.steps}</Typography>
                        <Slider
                          value={generateForm.steps}
                          onChange={(_e, value) => setGenerateForm({ ...generateForm, steps: Array.isArray(value) ? value[0] : value })}
                          min={1}
                          max={50}
                          step={1}
                          marks={[
                            { value: 4, label: 'Fast' },
                            { value: 8, label: 'Balanced' },
                            { value: 16, label: 'High Quality' },
                            { value: 32, label: 'Ultra' }
                          ]}
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading ? <CircularProgress size={20} /> : <GenerateIcon />}
                      onClick={handleGenerateSubmit}
                      disabled={loading || !generateForm.prompt.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading ? 'Generating...' : 'Generate Image'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Example Prompts
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {examplePrompts.map((prompt, index) => (
                        <Chip
                          key={index}
                          label={prompt.length > 50 ? prompt.substring(0, 50) + '...' : prompt}
                          onClick={() => setGenerateForm({ ...generateForm, prompt })}
                          sx={{ 
                            justifyContent: 'flex-start',
                            height: 'auto',
                            '& .MuiChip-label': {
                              display: 'block',
                              whiteSpace: 'normal',
                              textAlign: 'left',
                              padding: '8px 12px'
                            }
                          }}
                        />
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      )}

      {/* Overlay Image Editing Tab */}
      {activeTab === 1 && (
        <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
          <Box sx={{ p: 3 }}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <EditIcon color="primary" />
                  Image Editing & Overlay
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Base Image URL"
                      placeholder="https://example.com/base-image.jpg"
                      value={editForm.base_image_url}
                      onChange={(e) => setEditForm({ ...editForm, base_image_url: e.target.value })}
                      helperText="URL of the base image on which overlays will be placed"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                        Overlay Images ({editForm.overlay_images.length})
                      </Typography>
                      <Button
                        startIcon={<AddIcon />}
                        onClick={addOverlayImage}
                        variant="outlined"
                        size="small"
                      >
                        Add Overlay
                      </Button>
                    </Box>

                    {editForm.overlay_images.map((overlay, index) => (
                      <Accordion key={index} sx={{ mb: 2 }}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography>
                            Overlay {index + 1} {overlay.url ? `(${overlay.url.substring(0, 50)}...)` : '(Empty)'}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12}>
                              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                                <TextField
                                  fullWidth
                                  label="Image URL"
                                  placeholder="https://example.com/overlay.png"
                                  value={overlay.url}
                                  onChange={(e) => updateOverlayImage(index, 'url', e.target.value)}
                                />
                                <IconButton
                                  color="error"
                                  onClick={() => removeOverlayImage(index)}
                                  size="small"
                                >
                                  <RemoveIcon />
                                </IconButton>
                              </Box>
                            </Grid>

                            <Grid item xs={6} md={3}>
                              <Typography gutterBottom>X Position: {overlay.x}</Typography>
                              <Slider
                                value={overlay.x}
                                onChange={(_e, value) => updateOverlayImage(index, 'x', Array.isArray(value) ? value[0] : value)}
                                min={0}
                                max={1}
                                step={0.01}
                                size="small"
                              />
                            </Grid>

                            <Grid item xs={6} md={3}>
                              <Typography gutterBottom>Y Position: {overlay.y}</Typography>
                              <Slider
                                value={overlay.y}
                                onChange={(_e, value) => updateOverlayImage(index, 'y', Array.isArray(value) ? value[0] : value)}
                                min={0}
                                max={1}
                                step={0.01}
                                size="small"
                              />
                            </Grid>

                            <Grid item xs={6} md={3}>
                              <Typography gutterBottom>Width: {overlay.width}</Typography>
                              <Slider
                                value={overlay.width || 0.2}
                                onChange={(_e, value) => updateOverlayImage(index, 'width', Array.isArray(value) ? value[0] : value)}
                                min={0.05}
                                max={1}
                                step={0.01}
                                size="small"
                              />
                            </Grid>

                            <Grid item xs={6} md={3}>
                              <Typography gutterBottom>Opacity: {overlay.opacity}</Typography>
                              <Slider
                                value={overlay.opacity || 1}
                                onChange={(_e, value) => updateOverlayImage(index, 'opacity', Array.isArray(value) ? value[0] : value)}
                                min={0}
                                max={1}
                                step={0.01}
                                size="small"
                              />
                            </Grid>

                            <Grid item xs={6}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Rotation (degrees)"
                                value={overlay.rotation || 0}
                                onChange={(e) => updateOverlayImage(index, 'rotation', parseFloat(e.target.value))}
                                inputProps={{ min: 0, max: 359.99, step: 0.1 }}
                                size="small"
                              />
                            </Grid>

                            <Grid item xs={6}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Z-Index (layer order)"
                                value={overlay.z_index || 0}
                                onChange={(e) => updateOverlayImage(index, 'z_index', parseInt(e.target.value))}
                                inputProps={{ min: 0, max: 100 }}
                                size="small"
                              />
                            </Grid>
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Output Format</InputLabel>
                      <Select
                        value={editForm.output_format}
                        label="Output Format"
                        onChange={(e) => setEditForm({ ...editForm, output_format: e.target.value })}
                      >
                        <MenuItem value="png">PNG (Transparency support)</MenuItem>
                        <MenuItem value="jpg">JPEG (Smaller file size)</MenuItem>
                        <MenuItem value="webp">WebP (Modern format)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Quality: {editForm.output_quality}%</Typography>
                    <Slider
                      value={editForm.output_quality || 90}
                      onChange={(_e, value) => setEditForm({ ...editForm, output_quality: Array.isArray(value) ? value[0] : value })}
                      min={1}
                      max={100}
                      step={1}
                    />
                  </Grid>

                  {/* Image Stitching Settings */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                      Image Stitching Mode
                    </Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={editForm.stitch_mode || false}
                          onChange={(e) => setEditForm({ ...editForm, stitch_mode: e.target.checked })}
                        />
                      }
                      label="Enable Stitching Mode (combine images instead of overlaying)"
                      sx={{ mb: 2 }}
                    />

                    {editForm.stitch_mode && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={4}>
                          <FormControl fullWidth>
                            <InputLabel>Stitch Direction</InputLabel>
                            <Select
                              value={editForm.stitch_direction || 'horizontal'}
                              label="Stitch Direction"
                              onChange={(e) => setEditForm({ ...editForm, stitch_direction: e.target.value })}
                            >
                              <MenuItem value="horizontal">Horizontal</MenuItem>
                              <MenuItem value="vertical">Vertical</MenuItem>
                              <MenuItem value="grid">Grid</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} md={4}>
                          <Typography gutterBottom>Spacing: {editForm.stitch_spacing || 0}px</Typography>
                          <Slider
                            value={editForm.stitch_spacing || 0}
                            onChange={(_e, value) => setEditForm({ ...editForm, stitch_spacing: Array.isArray(value) ? value[0] : value })}
                            min={0}
                            max={100}
                            step={1}
                          />
                        </Grid>

                        <Grid item xs={12} md={4}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Max Width"
                            value={editForm.stitch_max_width || 1920}
                            onChange={(e) => setEditForm({ ...editForm, stitch_max_width: parseInt(e.target.value) })}
                            inputProps={{ min: 100, max: 4096 }}
                            size="small"
                          />
                          <TextField
                            fullWidth
                            type="number"
                            label="Max Height"
                            value={editForm.stitch_max_height || 1080}
                            onChange={(e) => setEditForm({ ...editForm, stitch_max_height: parseInt(e.target.value) })}
                            inputProps={{ min: 100, max: 4096 }}
                            size="small"
                            sx={{ mt: 1 }}
                          />
                        </Grid>
                      </Grid>
                    )}
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading ? <CircularProgress size={20} /> : <EditIcon />}
                  onClick={handleEditSubmit}
                  disabled={loading || !editForm.base_image_url.trim() || editForm.overlay_images.length === 0}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading ? 'Processing...' : 'Apply Edits'}
                </Button>
              </CardContent>
            </Card>
          </Box>
        </Paper>
      )}

      {/* AI Image Editing Tab (Flux) */}
      {activeTab === 2 && (
        <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
          <Box sx={{ p: 3 }}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FluxIcon color="primary" />
                  AI Image Editing with Flux
                </Typography>

                <Grid container spacing={3}>
                  {/* File Upload Section */}
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        Upload Image to Edit
                      </Typography>
                      <input
                        accept="image/*"
                        style={{ display: 'none' }}
                        id="image-upload"
                        type="file"
                        onChange={handleFileChange}
                      />
                      <label htmlFor="image-upload">
                        <Button
                          variant="outlined"
                          component="span"
                          startIcon={<UploadIcon />}
                          fullWidth
                          sx={{ 
                            py: 2,
                            borderStyle: 'dashed',
                            borderWidth: 2,
                            borderColor: selectedFile ? 'success.main' : 'grey.300'
                          }}
                        >
                          {selectedFile ? `Selected: ${selectedFile.name}` : 'Choose Image File (PNG, JPG, JPEG)'}
                        </Button>
                      </label>
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        Max file size: 10MB • Aspect ratio must be between 3:7 and 7:3
                      </Typography>
                    </Box>

                    {/* Image Preview */}
                    {previewUrl && (
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>Preview:</Typography>
                        <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#f8fafc' }}>
                          <img
                            src={previewUrl}
                            alt="Preview"
                            style={{
                              maxWidth: '100%',
                              maxHeight: '200px',
                              borderRadius: '8px',
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                            }}
                          />
                        </Paper>
                      </Box>
                    )}
                  </Grid>

                  {/* Editing Parameters */}
                  <Grid item xs={12} md={6}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Edit Description"
                          placeholder="Describe how you want to modify the image..."
                          value={fluxEditForm.prompt}
                          onChange={(e) => setFluxEditForm({ ...fluxEditForm, prompt: e.target.value })}
                          helperText="Be specific about the changes you want to make"
                        />
                      </Grid>

                      {/* Flux Image Provider Settings */}
                    </Grid>
                    
                    <ImageProviderSettings
                      imageProvider="flux"
                      searchSafety="moderate"
                      guidanceScale={fluxEditForm.guidance_scale || 3.5}
                      inferenceSteps={fluxEditForm.num_inference_steps || 20}
                      onImageProviderChange={() => {}} // Fixed to flux for editing
                      onSearchSafetyChange={() => {}} // Not used in editing
                      onGuidanceScaleChange={(value) => setFluxEditForm({ ...fluxEditForm, guidance_scale: value })}
                      onInferenceStepsChange={(value) => setFluxEditForm({ ...fluxEditForm, num_inference_steps: value })}
                      showAdvancedSettings={true}
                    />
                    
                    <Grid container spacing={2}>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Seed (Optional)"
                          placeholder="Leave empty for random"
                          value={fluxEditForm.seed || ''}
                          onChange={(e) => setFluxEditForm({ 
                            ...fluxEditForm, 
                            seed: e.target.value ? parseInt(e.target.value) : undefined 
                          })}
                          helperText="Use a specific seed for reproducible results"
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading ? <CircularProgress size={20} /> : <FluxIcon />}
                  onClick={handleFluxEditSubmit}
                  disabled={loading || !selectedFile || !fluxEditForm.prompt.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading ? 'Editing with AI...' : 'Edit Image with AI'}
                </Button>
              </CardContent>
            </Card>
          </Box>
        </Paper>
      )}

      {/* Results Section */}
      {(result || error || jobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                🎨 Image Processing Result
                {jobStatus && jobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {jobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {pollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={jobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: jobStatus === 'completed' ? 'success.main' : 
                           jobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {jobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {/* Success Results */}
              {result && jobStatus === 'completed' && result.result && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    🎉 {result.result.images ? 'Image search completed successfully!' : 'Image processed successfully!'}
                  </Alert>
                  
                  {/* Image Generation/Edit Preview - Only show for non-search results */}
                  {!result.result.images && (result.result.image_url || result.result.edited_image_url) && (
                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          {result.result.edited_image_url ? 'Edited Image' : 'Generated Image'}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            startIcon={<PreviewIcon />}
                            onClick={() => setPreviewDialog(true)}
                            variant="outlined"
                            size="small"
                          >
                            Full Size Preview
                          </Button>
                          {(result.result.edited_image_url || result.result.image_url) && (
                            <Button
                              startIcon={<DownloadIcon />}
                              href={result.result.edited_image_url || result.result.image_url || '#'}
                              component="a"
                              target="_blank"
                              variant="contained"
                              size="small"
                            >
                              Download
                            </Button>
                          )}
                          <Button
                            startIcon={<LibraryIcon />}
                            onClick={() => navigate('/dashboard/library')}
                            variant="outlined"
                            size="small"
                            color="primary"
                          >
                            View in Library
                          </Button>
                        </Box>
                      </Box>
                      
                      <Paper sx={{ p: 2, bgcolor: '#f8fafc', textAlign: 'center' }}>
                        <img
                          src={result.result.edited_image_url || result.result.image_url}
                          alt={result.result.edited_image_url ? "Edited result" : "Generated result"}
                          style={{
                            maxWidth: '100%',
                            maxHeight: '400px',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                          }}
                        />
                      </Paper>

                      {/* Show original image for editing results */}
                      {result.result.original_image_url && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>Original Image:</Typography>
                          <Paper sx={{ p: 2, bgcolor: '#f8fafc', textAlign: 'center' }}>
                            <img
                              src={result.result.original_image_url}
                              alt="Original image"
                              style={{
                                maxWidth: '100%',
                                maxHeight: '200px',
                                borderRadius: '8px',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                              }}
                            />
                          </Paper>
                        </Box>
                      )}
                    </Box>
                  )}
                  
                  {/* Processing Details */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Processing Details:</Typography>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        {/* Search-specific details */}
                        {result.result.images && (
                          <>
                            {result.result.query_used && (
                              <Grid item xs={12}>
                                <strong>Search Query:</strong> {result.result.query_used}
                              </Grid>
                            )}
                            {result.result.provider_used && (
                              <Grid item xs={12} md={6}>
                                <strong>Provider:</strong> {result.result.provider_used.charAt(0).toUpperCase() + result.result.provider_used.slice(1)}
                              </Grid>
                            )}
                            {result.result.total_results && (
                              <Grid item xs={12} md={6}>
                                <strong>Total Results:</strong> {result.result.total_results}
                              </Grid>
                            )}
                            {result.result.per_page && (
                              <Grid item xs={12} md={6}>
                                <strong>Results Per Page:</strong> {result.result.per_page}
                              </Grid>
                            )}
                            {result.result.page && (
                              <Grid item xs={12} md={6}>
                                <strong>Page:</strong> {result.result.page}
                              </Grid>
                            )}
                          </>
                        )}
                        
                        {/* Image generation/editing details */}
                        {!result.result.images && (
                          <>
                            {result.result.prompt_used && (
                              <Grid item xs={12}>
                                <strong>Prompt Used:</strong> {result.result.prompt_used}
                              </Grid>
                            )}
                            {result.result.model_used && (
                              <Grid item xs={12} md={6}>
                                <strong>Model:</strong> {result.result.model_used}
                              </Grid>
                            )}
                            {result.result.dimensions && (
                              <Grid item xs={12} md={6}>
                                <strong>Dimensions:</strong> {result.result.dimensions.width} × {result.result.dimensions.height}
                              </Grid>
                            )}
                            {result.result.width && result.result.height && (
                              <Grid item xs={12} md={6}>
                                <strong>Dimensions:</strong> {result.result.width} × {result.result.height}
                              </Grid>
                            )}
                            {result.result.format && (
                              <Grid item xs={12} md={6}>
                                <strong>Format:</strong> {result.result.format.toUpperCase()}
                              </Grid>
                            )}
                            {result.result.processing_time && (
                              <Grid item xs={12} md={6}>
                                <strong>Processing Time:</strong> {result.result.processing_time.toFixed(1)}s
                              </Grid>
                            )}
                            {result.result.storage_path && (
                              <Grid item xs={12} md={6}>
                                <strong>Storage Path:</strong> {result.result.storage_path}
                              </Grid>
                            )}
                          </>
                        )}
                      </Grid>
                    </Paper>
                  </Box>
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {result && !result.result && jobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Image processing job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {result.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Image Enhancement Tab */}
      {activeTab === 3 && (
        <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
          <Box sx={{ p: 3 }}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <EnhanceIcon color="primary" />
                  Image Enhancement & Artifact Removal
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Image URL"
                      placeholder="https://example.com/ai-generated-image.jpg"
                      value={enhanceForm.image_url}
                      onChange={(e) => setEnhanceForm({ ...enhanceForm, image_url: e.target.value })}
                      helperText="URL of the image to enhance and remove AI artifacts from"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Color Enhancement: {enhanceForm.enhance_color}</Typography>
                    <Slider
                      value={enhanceForm.enhance_color || 1.0}
                      onChange={(_e, value) => setEnhanceForm({ ...enhanceForm, enhance_color: Array.isArray(value) ? value[0] : value })}
                      min={0.0}
                      max={2.0}
                      step={0.1}
                      marks={[
                        { value: 0.0, label: 'B&W' },
                        { value: 1.0, label: 'Normal' },
                        { value: 2.0, label: 'Enhanced' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Contrast Enhancement: {enhanceForm.enhance_contrast}</Typography>
                    <Slider
                      value={enhanceForm.enhance_contrast || 1.0}
                      onChange={(_e, value) => setEnhanceForm({ ...enhanceForm, enhance_contrast: Array.isArray(value) ? value[0] : value })}
                      min={0.0}
                      max={2.0}
                      step={0.1}
                      marks={[
                        { value: 0.0, label: 'Low' },
                        { value: 1.0, label: 'Normal' },
                        { value: 2.0, label: 'High' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Noise/Grain Strength: {enhanceForm.noise_strength}</Typography>
                    <Slider
                      value={enhanceForm.noise_strength || 10}
                      onChange={(_e, value) => setEnhanceForm({ ...enhanceForm, noise_strength: Array.isArray(value) ? value[0] : value })}
                      min={0}
                      max={100}
                      step={1}
                      marks={[
                        { value: 0, label: 'None' },
                        { value: 50, label: 'Medium' },
                        { value: 100, label: 'High' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Vintage Effect: {enhanceForm.vintage_effect}</Typography>
                    <Slider
                      value={enhanceForm.vintage_effect || 0.0}
                      onChange={(_e, value) => setEnhanceForm({ ...enhanceForm, vintage_effect: Array.isArray(value) ? value[0] : value })}
                      min={0.0}
                      max={1.0}
                      step={0.1}
                      marks={[
                        { value: 0.0, label: 'None' },
                        { value: 0.5, label: 'Medium' },
                        { value: 1.0, label: 'Strong' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={enhanceForm.remove_artifacts || true}
                          onChange={(e) => setEnhanceForm({ ...enhanceForm, remove_artifacts: e.target.checked })}
                        />
                      }
                      label="Remove AI Artifacts"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={enhanceForm.add_film_grain || false}
                          onChange={(e) => setEnhanceForm({ ...enhanceForm, add_film_grain: e.target.checked })}
                        />
                      }
                      label="Add Film Grain Effect"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Output Format</InputLabel>
                      <Select
                        value={enhanceForm.output_format || 'png'}
                        label="Output Format"
                        onChange={(e) => setEnhanceForm({ ...enhanceForm, output_format: e.target.value })}
                      >
                        <MenuItem value="png">PNG (Best Quality)</MenuItem>
                        <MenuItem value="jpg">JPEG (Smaller Size)</MenuItem>
                        <MenuItem value="webp">WebP (Modern Format)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading ? <CircularProgress size={20} /> : <EnhanceIcon />}
                  onClick={handleEnhancementSubmit}
                  disabled={loading || !enhanceForm.image_url.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading ? 'Processing...' : 'Enhance Image'}
                </Button>
              </CardContent>
            </Card>
          </Box>
        </Paper>
      )}

      {/* Image Search Tab */}
      {activeTab === 4 && (
        <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
          <Box sx={{ p: 3 }}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SearchIcon color="primary" />
                  Search Stock Images
                </Typography>

                <Grid container spacing={3}>
                  {/* Search Form */}
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Search Query"
                      placeholder="Enter keywords to search for images..."
                      value={searchForm.query}
                      onChange={(e) => setSearchForm({ ...searchForm, query: e.target.value })}
                      variant="outlined"
                      sx={{ mb: 2 }}
                    />

                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel>Provider</InputLabel>
                      <Select
                        value={searchForm.provider}
                        label="Provider"
                        onChange={(e) => setSearchForm({ ...searchForm, provider: e.target.value })}
                      >
                        <MenuItem value="pexels">Pexels</MenuItem>
                        <MenuItem value="pixabay">Pixabay</MenuItem>
                      </Select>
                    </FormControl>

                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={6}>
                        <FormControl fullWidth>
                          <InputLabel>Category</InputLabel>
                          <Select
                            value={searchForm.category}
                            label="Category"
                            onChange={(e) => setSearchForm({ ...searchForm, category: e.target.value })}
                          >
                            <MenuItem value="all">All Categories</MenuItem>
                            <MenuItem value="backgrounds">Backgrounds</MenuItem>
                            <MenuItem value="fashion">Fashion</MenuItem>
                            <MenuItem value="nature">Nature</MenuItem>
                            <MenuItem value="science">Science</MenuItem>
                            <MenuItem value="education">Education</MenuItem>
                            <MenuItem value="people">People</MenuItem>
                            <MenuItem value="religion">Religion</MenuItem>
                            <MenuItem value="places">Places</MenuItem>
                            <MenuItem value="animals">Animals</MenuItem>
                            <MenuItem value="industry">Industry</MenuItem>
                            <MenuItem value="computer">Computer</MenuItem>
                            <MenuItem value="food">Food</MenuItem>
                            <MenuItem value="sports">Sports</MenuItem>
                            <MenuItem value="transportation">Transportation</MenuItem>
                            <MenuItem value="travel">Travel</MenuItem>
                            <MenuItem value="buildings">Buildings</MenuItem>
                            <MenuItem value="business">Business</MenuItem>
                            <MenuItem value="music">Music</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={6}>
                        <FormControl fullWidth>
                          <InputLabel>Orientation</InputLabel>
                          <Select
                            value={searchForm.orientation}
                            label="Orientation"
                            onChange={(e) => setSearchForm({ ...searchForm, orientation: e.target.value })}
                          >
                            <MenuItem value="all">All Orientations</MenuItem>
                            <MenuItem value="horizontal">Horizontal</MenuItem>
                            <MenuItem value="vertical">Vertical</MenuItem>
                            <MenuItem value="square">Square</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>

                    <Grid container spacing={2} sx={{ mb: 3 }}>
                      <Grid item xs={6}>
                        <FormControl fullWidth>
                          <InputLabel>Size</InputLabel>
                          <Select
                            value={searchForm.size}
                            label="Size"
                            onChange={(e) => setSearchForm({ ...searchForm, size: e.target.value })}
                          >
                            <MenuItem value="all">All Sizes</MenuItem>
                            <MenuItem value="large">Large</MenuItem>
                            <MenuItem value="medium">Medium</MenuItem>
                            <MenuItem value="small">Small</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={6}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Number of Results"
                          value={searchForm.limit}
                          onChange={(e) => setSearchForm({ ...searchForm, limit: parseInt(e.target.value) || 20 })}
                          inputProps={{ min: 1, max: 200 }}
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
                      onClick={handleSearchSubmit}
                      disabled={loading || !searchForm.query.trim()}
                      sx={{ px: 4 }}
                    >
                      {loading ? 'Searching...' : 'Search Images'}
                    </Button>
                  </Grid>

                  {/* Search Results */}
                  <Grid item xs={12} md={6}>
                    {searchResults && searchResults.length > 0 && (
                      <Box>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          Search Results ({searchResults.length} images)
                        </Typography>
                        <Grid container spacing={2}>
                          {searchResults.map((image) => (
                            <Grid item xs={6} key={image.id}>
                              <Card elevation={1}>
                                <Box sx={{ position: 'relative', paddingTop: '75%' }}>
                                  <img
                                    src={image.url}
                                    alt={image.alt || 'Stock image'}
                                    style={{
                                      position: 'absolute',
                                      top: 0,
                                      left: 0,
                                      width: '100%',
                                      height: '100%',
                                      objectFit: 'cover',
                                      borderRadius: '4px 4px 0 0'
                                    }}
                                  />
                                </Box>
                                <CardContent sx={{ p: 1 }}>
                                  {image.photographer && (
                                    <Typography variant="caption" sx={{ display: 'block', mb: 0.5 }}>
                                      by {image.photographer}
                                    </Typography>
                                  )}
                                  <Box sx={{ display: 'flex', gap: 1 }}>
                                    <Button
                                      size="small"
                                      variant="outlined"
                                      href={image.url}
                                      target="_blank"
                                      startIcon={<PreviewIcon />}
                                      sx={{ fontSize: '0.7rem', minWidth: 'auto', px: 1 }}
                                    >
                                      View
                                    </Button>
                                    <Button
                                      size="small"
                                      variant="contained"
                                      href={image.download_url}
                                      target="_blank"
                                      startIcon={<DownloadIcon />}
                                      sx={{ fontSize: '0.7rem', minWidth: 'auto', px: 1 }}
                                    >
                                      Download
                                    </Button>
                                  </Box>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    )}

                    {searchResults && searchResults.length === 0 && (
                      <Alert severity="info" sx={{ mt: 2 }}>
                        No images found for your search query. Try different keywords or settings.
                      </Alert>
                    )}
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Box>
        </Paper>
      )}

      {/* Full Size Preview Dialog */}
      <Dialog
        open={previewDialog}
        onClose={() => setPreviewDialog(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>Image Preview</DialogTitle>
        <DialogContent>
          {(result?.result?.image_url || result?.result?.edited_image_url) && (
            <img
              src={result.result.edited_image_url || result.result.image_url}
              alt="Full size preview"
              style={{
                width: '100%',
                height: 'auto',
                borderRadius: '8px'
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>Close</Button>
          {(result?.result?.image_url || result?.result?.edited_image_url) && (
            <Button
              href={result.result.edited_image_url || result.result.image_url || '#'}
              component="a"
              target="_blank"
              variant="contained"
              startIcon={<DownloadIcon />}
            >
              Download
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Images;
