import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Pagination,
  InputAdornment,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Avatar,
  Tooltip,
  CardActionArea,
  Stack,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  VideoLibrary as VideoIcon,
  AudioFile as AudioIcon,
  Image as ImageIcon,
  TextSnippet as TextIcon,
  PlayArrow as PlayIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  GetApp as GetAppIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

import { directApi } from '../utils/api';

// Content Types
export enum ContentType {
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
  TEXT = 'text',
  ALL = 'all'
}

// Content Item Interface
interface ContentItem {
  job_id: string;
  job_type: string;
  content_type: ContentType;
  title?: string;
  description?: string;
  file_url: string;
  thumbnail_url?: string;
  file_size?: number;
  duration?: number;
  dimensions?: { width: number; height: number };
  created_at: string;
  updated_at: string;
  metadata: Record<string, any>;
  parameters: Record<string, any>;
}

// Library Response Interface
interface LibraryResponse {
  content: ContentItem[];
  total_count: number;
  content_type_filter: ContentType;
  pagination: {
    limit: number;
    offset: number;
    total_count: number;
    has_next: boolean;
    has_previous: boolean;
    next_offset?: number;
    previous_offset?: number;
  };
}

// Stats Interface
interface LibraryStats {
  stats: {
    video: number;
    audio: number;
    image: number;
    text: number;
    total: number;
  };
  total_items: number;
}

const Library: React.FC = () => {
  // State
  const [content, setContent] = useState<ContentItem[]>([]);
  const [stats, setStats] = useState<LibraryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [contentTypeFilter, setContentTypeFilter] = useState<ContentType>(ContentType.ALL);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedItem, setSelectedItem] = useState<ContentItem | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);

  const itemsPerPage = 20;

  // Content type configuration
  const contentTypeConfig = {
    [ContentType.ALL]: { label: 'All Content', icon: <FilterIcon />, color: 'default' as const },
    [ContentType.VIDEO]: { label: 'Videos', icon: <VideoIcon />, color: 'primary' as const },
    [ContentType.AUDIO]: { label: 'Audio', icon: <AudioIcon />, color: 'secondary' as const },
    [ContentType.IMAGE]: { label: 'Images', icon: <ImageIcon />, color: 'success' as const },
    [ContentType.TEXT]: { label: 'Text', icon: <TextIcon />, color: 'info' as const },
  };

  // Fetch library content
  const fetchLibraryContent = async (
    page: number = 1,
    contentType: ContentType = ContentType.ALL,
    search: string = ''
  ) => {
    try {
      setLoading(true);
      setError(null);

      const offset = (page - 1) * itemsPerPage;
      const params = new URLSearchParams({
        limit: itemsPerPage.toString(),
        offset: offset.toString(),
        content_type: contentType
      });

      if (search.trim()) {
        params.append('search', search.trim());
      }

      const response = await directApi.get(`/api/v1/library/content?${params}`);
      const data: LibraryResponse = response.data;

      setContent(data.content);
      setTotalItems(data.total_count);
      setTotalPages(Math.ceil(data.total_count / itemsPerPage));

    } catch (err) {
      console.error('Error fetching library content:', err);
      setError('Failed to load library content');
      setContent([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch library stats
  const fetchLibraryStats = async () => {
    try {
      const response = await directApi.get('/api/v1/library/stats');
      setStats(response.data);
    } catch (err) {
      console.error('Error fetching library stats:', err);
    }
  };

  // Initial load
  useEffect(() => {
    fetchLibraryContent(currentPage, contentTypeFilter, searchQuery);
    fetchLibraryStats();
  }, []);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
    fetchLibraryContent(1, contentTypeFilter, query);
  };

  // Handle content type filter
  const handleContentTypeFilter = (type: ContentType) => {
    setContentTypeFilter(type);
    setCurrentPage(1);
    fetchLibraryContent(1, type, searchQuery);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchLibraryContent(page, contentTypeFilter, searchQuery);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchLibraryContent(currentPage, contentTypeFilter, searchQuery);
    fetchLibraryStats();
  };

  // Handle favorite toggle
  const handleFavoriteToggle = async (item: ContentItem, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    try {
      await directApi.post(`/api/v1/library/favorite/${item.job_id}`);
      // Update the item in the content array
      setContent(prevContent => 
        prevContent.map(contentItem => 
          contentItem.job_id === item.job_id 
            ? { ...contentItem, metadata: { ...contentItem.metadata, is_favorite: !contentItem.metadata.is_favorite }}
            : contentItem
        )
      );
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  // Handle delete
  const handleDelete = (item: ContentItem, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    setItemToDelete(item.job_id);
    setDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!itemToDelete) return;
    
    try {
      await directApi.delete(`/api/v1/library/content/${itemToDelete}`);
      // Remove the item from the content array
      setContent(prevContent => prevContent.filter(item => item.job_id !== itemToDelete));
      setTotalItems(prev => prev - 1);
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  // Format file size
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return 'Unknown';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  // Format duration
  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get content type icon and color
  const getContentTypeDisplay = (type: ContentType) => {
    const config = contentTypeConfig[type];
    return {
      icon: config.icon,
      label: config.label,
      color: config.color
    };
  };

  // Handle item click
  const handleItemClick = (item: ContentItem) => {
    setSelectedItem(item);
    setPreviewOpen(true);
  };

  // Close preview
  const closePreview = () => {
    setPreviewOpen(false);
    setSelectedItem(null);
  };

  // Download file
  const handleDownload = (item: ContentItem, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    if (item.file_url) {
      window.open(item.file_url, '_blank');
    }
  };

  // Render content grid
  const renderContentGrid = () => (
    <Grid container spacing={3}>
      {content.map((item) => {
        const typeDisplay = getContentTypeDisplay(item.content_type);
        return (
          <Grid item xs={12} sm={6} md={4} lg={3} key={item.job_id}>
            <Card 
              sx={{ 
                height: '100%',
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                }
              }}
            >
              <CardActionArea onClick={() => handleItemClick(item)} sx={{ height: '100%' }}>
                {/* Thumbnail */}
                <Box sx={{ position: 'relative', height: 180, bgcolor: 'grey.100' }}>
                  {item.thumbnail_url ? (
                    <CardMedia
                      component="img"
                      height="180"
                      image={item.thumbnail_url}
                      alt={item.title || 'Content thumbnail'}
                      sx={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <Box 
                      sx={{ 
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'grey.50'
                      }}
                    >
                      <Avatar 
                        sx={{ 
                          width: 64, 
                          height: 64, 
                          bgcolor: `${typeDisplay.color}.light` 
                        }}
                      >
                        {typeDisplay.icon}
                      </Avatar>
                    </Box>
                  )}
                  
                  {/* Content type badge */}
                  <Chip
                    size="small"
                    label={typeDisplay.label}
                    color={typeDisplay.color}
                    sx={{ position: 'absolute', top: 8, right: 8 }}
                  />

                  {/* Favorite indicator */}
                  {item.metadata.is_favorite && (
                    <FavoriteIcon
                      sx={{ 
                        position: 'absolute', 
                        top: 8, 
                        left: 8,
                        color: 'error.main',
                        bgcolor: 'rgba(255,255,255,0.9)',
                        borderRadius: '50%',
                        p: 0.5
                      }}
                    />
                  )}

                  {/* Duration badge for video/audio */}
                  {item.duration && (
                    <Chip
                      size="small"
                      label={formatDuration(item.duration)}
                      sx={{ 
                        position: 'absolute', 
                        bottom: 8, 
                        right: 8,
                        bgcolor: 'rgba(0,0,0,0.7)',
                        color: 'white'
                      }}
                    />
                  )}
                </Box>

                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" noWrap gutterBottom>
                    {item.title || item.job_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Typography>
                  
                  {item.description && (
                    <Typography 
                      variant="body2" 
                      color="text.secondary" 
                      sx={{ 
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}
                    >
                      {item.description}
                    </Typography>
                  )}

                  <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
                    {item.file_size && (
                      <Chip size="small" label={formatFileSize(item.file_size)} />
                    )}
                    {item.dimensions && (
                      <Chip 
                        size="small" 
                        label={`${item.dimensions.width}×${item.dimensions.height}`} 
                      />
                    )}
                  </Stack>

                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    {new Date(item.created_at).toLocaleDateString()}
                  </Typography>
                </CardContent>
              </CardActionArea>

              <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                <Box>
                  <Tooltip title={item.metadata.is_favorite ? "Remove from favorites" : "Add to favorites"}>
                    <IconButton
                      size="small"
                      onClick={(e) => handleFavoriteToggle(item, e)}
                      color={item.metadata.is_favorite ? "error" : "default"}
                    >
                      {item.metadata.is_favorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Download">
                    <IconButton
                      size="small"
                      onClick={(e) => handleDownload(item, e)}
                      color="primary"
                    >
                      <DownloadIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Box>
                  <Tooltip title="View Details">
                    <IconButton
                      size="small"
                      onClick={() => handleItemClick(item)}
                      color="secondary"
                    >
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete">
                    <IconButton
                      size="small"
                      onClick={(e) => handleDelete(item, e)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </CardActions>
            </Card>
          </Grid>
        );
      })}
    </Grid>
  );

  // Render content list
  const renderContentList = () => (
    <Stack spacing={2}>
      {content.map((item) => {
        const typeDisplay = getContentTypeDisplay(item.content_type);
        return (
          <Card key={item.job_id} sx={{ cursor: 'pointer' }}>
            <CardActionArea onClick={() => handleItemClick(item)}>
              <CardContent>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Avatar sx={{ bgcolor: `${typeDisplay.color}.light` }}>
                    {typeDisplay.icon}
                  </Avatar>
                  
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6">
                      {item.title || item.job_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {item.description || `${typeDisplay.label} • ${new Date(item.created_at).toLocaleDateString()}`}
                    </Typography>
                  </Box>

                  <Stack direction="row" spacing={1} alignItems="center">
                    {item.duration && (
                      <Chip size="small" label={formatDuration(item.duration)} />
                    )}
                    {item.file_size && (
                      <Chip size="small" label={formatFileSize(item.file_size)} />
                    )}
                    <IconButton
                      onClick={(e) => handleFavoriteToggle(item, e)}
                      size="small"
                      color={item.metadata.is_favorite ? "error" : "default"}
                    >
                      {item.metadata.is_favorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                    </IconButton>
                    <IconButton
                      onClick={(e) => handleDownload(item, e)}
                      size="small"
                    >
                      <DownloadIcon />
                    </IconButton>
                    <IconButton
                      onClick={(e) => handleDelete(item, e)}
                      size="small"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Stack>
                </Stack>
              </CardContent>
            </CardActionArea>
          </Card>
        );
      })}
    </Stack>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Content Library
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Browse and manage all your generated content
        </Typography>
      </Box>

      {/* Stats Cards */}
      {stats && (
        <Grid container spacing={2} sx={{ mb: 4 }}>
          {Object.entries(contentTypeConfig).filter(([key]) => key !== ContentType.ALL).map(([type, config]) => (
            <Grid item xs={6} sm={3} key={type}>
              <Card 
                sx={{ 
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 }
                }}
                onClick={() => handleContentTypeFilter(type as ContentType)}
              >
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Avatar sx={{ bgcolor: `${config.color}.light`, mx: 'auto', mb: 1 }}>
                    {config.icon}
                  </Avatar>
                  <Typography variant="h5">
                    {stats.stats[type as keyof typeof stats.stats] || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {config.label}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Filters and Search */}
      <Box sx={{ mb: 3 }}>
        <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
          <TextField
            placeholder="Search content..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            size="small"
            sx={{ minWidth: 300 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Content Type</InputLabel>
            <Select
              value={contentTypeFilter}
              onChange={(e) => handleContentTypeFilter(e.target.value as ContentType)}
              label="Content Type"
            >
              {Object.entries(contentTypeConfig).map(([type, config]) => (
                <MenuItem key={type} value={type}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    {config.icon}
                    <span>{config.label}</span>
                  </Stack>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Box sx={{ flexGrow: 1 }} />

          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="View Mode">
            <IconButton onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
              {viewMode === 'grid' ? <ListViewIcon /> : <GridViewIcon />}
            </IconButton>
          </Tooltip>
        </Stack>

        {/* Content type tabs */}
        <Tabs
          value={contentTypeFilter}
          onChange={(_, newValue) => handleContentTypeFilter(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {Object.entries(contentTypeConfig).map(([type, config]) => (
            <Tab
              key={type}
              value={type}
              label={config.label}
              icon={config.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      {/* Content */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : content.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary">
            No content found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchQuery ? 'Try adjusting your search or filters' : 'Start creating content to see it here'}
          </Typography>
        </Box>
      ) : (
        <>
          {viewMode === 'grid' ? renderContentGrid() : renderContentList()}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={(_, page) => handlePageChange(page)}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={closePreview}
        maxWidth="md"
        fullWidth
      >
        {selectedItem && (
          <>
            <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6">
                {selectedItem.title || selectedItem.job_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Typography>
              <IconButton onClick={closePreview}>
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent dividers>
              <Stack spacing={3}>
                {/* Content preview */}
                {selectedItem.content_type === ContentType.IMAGE && (
                  <Box sx={{ textAlign: 'center' }}>
                    <img
                      src={selectedItem.file_url}
                      alt="Content"
                      style={{ maxWidth: '100%', maxHeight: '400px', objectFit: 'contain' }}
                    />
                  </Box>
                )}
                {selectedItem.content_type === ContentType.VIDEO && (
                  <Box sx={{ textAlign: 'center' }}>
                    <video
                      controls
                      style={{ maxWidth: '100%', maxHeight: '400px' }}
                      preload="metadata"
                    >
                      <source src={selectedItem.file_url} type="video/mp4" />
                    </video>
                  </Box>
                )}
                {selectedItem.content_type === ContentType.AUDIO && (
                  <Box sx={{ textAlign: 'center' }}>
                    <audio controls style={{ width: '100%' }}>
                      <source src={selectedItem.file_url} />
                    </audio>
                  </Box>
                )}

                {/* Metadata */}
                <Box>
                  <Typography variant="h6" gutterBottom>Details</Typography>
                  <Stack spacing={1}>
                    <Typography><strong>Type:</strong> {getContentTypeDisplay(selectedItem.content_type).label}</Typography>
                    <Typography><strong>Created:</strong> {new Date(selectedItem.created_at).toLocaleString()}</Typography>
                    {selectedItem.file_size && (
                      <Typography><strong>File Size:</strong> {formatFileSize(selectedItem.file_size)}</Typography>
                    )}
                    {selectedItem.duration && (
                      <Typography><strong>Duration:</strong> {formatDuration(selectedItem.duration)}</Typography>
                    )}
                    {selectedItem.dimensions && (
                      <Typography><strong>Dimensions:</strong> {selectedItem.dimensions.width}×{selectedItem.dimensions.height}px</Typography>
                    )}
                    {selectedItem.description && (
                      <Typography><strong>Description:</strong> {selectedItem.description}</Typography>
                    )}
                  </Stack>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions>
              <Button
                startIcon={<GetAppIcon />}
                onClick={() => handleDownload(selectedItem)}
                variant="contained"
              >
                Download
              </Button>
              <Button onClick={closePreview}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Delete Content</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this content item? This action cannot be undone.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Note: The file will remain in S3 storage, only the library entry will be removed.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete}>Cancel</Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Library;