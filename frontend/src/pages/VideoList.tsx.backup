import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  // Paper, 
  Button, 
  CircularProgress, 
  Alert,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Pagination,
  InputAdornment,
  TextField
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';
import SearchIcon from '@mui/icons-material/Search';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';

import { directApi, apiUtils } from '../utils/api';
import { Job, JobStatus, JobType } from '../types/ouinhi';

interface VideoListState {
  jobs: Job[];
  loading: boolean;
  error: string | null;
  page: number;
  total: number;
  deleteDialogOpen: boolean;
  jobToDelete: string | null;
  refreshing: boolean;
  deletedJobs: Set<string>; // Track deleted job IDs
}

const VideoList: React.FC = () => {
  const navigate = useNavigate();
  
  const [state, setState] = useState<VideoListState>({
    jobs: [],
    loading: true,
    error: null,
    page: 1,
    total: 0,
    deleteDialogOpen: false,
    jobToDelete: null,
    refreshing: false,
    deletedJobs: new Set<string>()
  });

  const [searchTerm, setSearchTerm] = useState('');
  // Removed status filter since we only show completed videos

  const fetchJobs = async (page = 1, showLoader = true) => {
    try {
      if (showLoader) {
        setState(prev => ({ ...prev, loading: true, error: null }));
      } else {
        setState(prev => ({ ...prev, refreshing: true, error: null }));
      }

      // Fetch more jobs to ensure we get all video jobs (since we filter client-side)
      const response = await directApi.listJobs(1, 100); // Get first 100 jobs
      
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch jobs');
      }

      const { jobs, total } = response.data;
      
      // Filter jobs to only show completed Script and Topic videos
      const videoJobs = jobs.filter(job => {
        const jobId = getJobId(job);
        const isScriptOrTopicVideo = job.operation === JobType.AIIMAGE_TO_VIDEO || 
                                    job.operation === JobType.FOOTAGE_TO_VIDEO;
        const isCompleted = job.status === JobStatus.COMPLETED;
        const isNotDeleted = !state.deletedJobs.has(jobId);
        return isScriptOrTopicVideo && isCompleted && isNotDeleted;
      });

      setState(prev => ({
        ...prev,
        jobs: videoJobs,
        total: videoJobs.length, // Use actual filtered count for proper pagination
        page: 1, // Reset to first page when fetching new data
        loading: false,
        refreshing: false
      }));
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: apiUtils.formatError(err),
        loading: false,
        refreshing: false
      }));
    }
  };

  useEffect(() => {
    fetchJobs(1);
  }, []);

  const handleCreateNew = () => {
    navigate('/dashboard/create');
  };

  const handleCreateWithResearch = () => {
    navigate('/dashboard/research');
  };

  const getJobId = (job: Job): string => {
    return job.job_id || job.id || '';
  };

  const handleJobClick = (jobId: string) => {
    navigate(`/dashboard/library/${jobId}`);
  };

  const handleDeleteJob = (jobId: string) => {
    setState(prev => ({ 
      ...prev, 
      deleteDialogOpen: true, 
      jobToDelete: jobId 
    }));
  };

  const confirmDelete = async () => {
    if (!state.jobToDelete) return;
    
    // Capture the job ID before updating state
    const deletedJobId = state.jobToDelete;
    
    // Add to deleted jobs set immediately (optimistic update)
    setState(prev => {
      const newDeletedJobs = new Set(prev.deletedJobs);
      newDeletedJobs.add(deletedJobId);
      
      const filteredJobs = prev.jobs.filter(job => {
        const jobId = getJobId(job);
        return jobId !== deletedJobId;
      });
      
      return { 
        ...prev, 
        jobs: filteredJobs,
        total: Math.max(0, prev.total - 1),
        deleteDialogOpen: false, 
        jobToDelete: null,
        deletedJobs: newDeletedJobs
      };
    });
    
    // Try to delete from backend in background (fire and forget)
    try {
      await directApi.deleteJob(deletedJobId);
    } catch (err) {
      // Ignore backend errors - the UI is already updated
    }
  };

  const handleRefresh = () => {
    fetchJobs(state.page, false);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setState(prev => ({ ...prev, page: value }));
  };

  const getStatusColor = (status: JobStatus) => {
    switch (status) {
      case JobStatus.COMPLETED:
        return 'success';
      case JobStatus.PROCESSING:
        return 'primary';
      case JobStatus.FAILED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getJobTypeDisplay = (operation: string) => {
    switch (operation) {
      case JobType.SHORT_VIDEO_CREATION:
        return 'Short Video';
      case JobType.FOOTAGE_TO_VIDEO:
        return 'Topic Video';
      case JobType.AIIMAGE_TO_VIDEO:
        return 'Script Video';
      case JobType.AI_SCRIPT_GENERATION:
        return 'AI Script';
      default:
        return operation.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const filteredJobs = state.jobs.filter(job => {
    const matchesSearch = !searchTerm || 
      getJobId(job).toLowerCase().includes(searchTerm.toLowerCase()) ||
      getJobTypeDisplay(job.operation).toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  // Client-side pagination
  const itemsPerPage = 12;
  const totalPages = Math.ceil(filteredJobs.length / itemsPerPage);
  const startIndex = (state.page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedJobs = filteredJobs.slice(startIndex, endIndex);

  if (state.loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="60vh">
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Video Library
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Your completed Script and Topic videos
          </Typography>
        </Box>
        
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<SearchIcon />}
            onClick={handleCreateWithResearch}
          >
            AI Research
          </Button>
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />}
            onClick={handleCreateNew}
          >
            Create Video
          </Button>
        </Box>
      </Box>
      
      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={10}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search your video library..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  // Reset to first page when searching
                  setState(prev => ({ ...prev, page: 1 }));
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={state.refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
                onClick={handleRefresh}
                disabled={state.refreshing}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {state.error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {state.error}
        </Alert>
      )}
      
      {/* Empty State */}
      {filteredJobs.length === 0 && !state.loading ? (
        <Card sx={{ p: 4, textAlign: 'center' }}>
          <CardContent>
            <VideoLibraryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              {state.jobs.length === 0 ? 'No completed videos yet' : 'No videos match your search'}
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              {state.jobs.length === 0 
                ? "You haven't created any completed videos yet. Get started by creating your first video!"
                : "Try adjusting your search criteria."
              }
            </Typography>
            {state.jobs.length === 0 && (
              <Box sx={{ mt: 3 }}>
                <Button 
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateNew}
                  sx={{ mr: 2 }}
                >
                  Create Your First Video
                </Button>
                <Button 
                  variant="outlined"
                  startIcon={<SearchIcon />}
                  onClick={handleCreateWithResearch}
                >
                  Try AI Research
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Video Grid */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {paginatedJobs.map((job) => (
              <Grid item xs={12} sm={6} md={4} key={getJobId(job)}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    transition: 'elevation 0.2s',
                    '&:hover': {
                      elevation: 4,
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => handleJobClick(getJobId(job))}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box display="flex" justifyContent="flex-start" alignItems="flex-start" mb={2}>
                      <Chip
                        label={getJobTypeDisplay(job.operation)}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>
                    
                    <Typography variant="h6" gutterBottom noWrap>
                      Video {getJobId(job).substring(0, 8) || 'Unknown'}...
                    </Typography>
                    
                    {job.result?.video_duration && (
                      <Typography variant="body2" color="text.secondary">
                        Duration: {formatDuration(job.result.video_duration)}
                      </Typography>
                    )}
                    
                    {job.result?.word_count && (
                      <Typography variant="body2" color="text.secondary">
                        {job.result.word_count} words
                      </Typography>
                    )}
                    
                    {job.created_at && (
                      <Typography variant="body2" color="text.secondary">
                        Created: {new Date(job.created_at).toLocaleDateString()}
                      </Typography>
                    )}
                  </CardContent>
                  
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<PlayArrowIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleJobClick(getJobId(job));
                      }}
                    >
                      View Video
                    </Button>
                    
                    <IconButton
                      size="small"
                      color="error"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteJob(getJobId(job));
                      }}
                      sx={{ ml: 'auto' }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box display="flex" justifyContent="center">
              <Pagination
                count={totalPages}
                page={state.page}
                onChange={handlePageChange}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={state.deleteDialogOpen}
        onClose={() => setState(prev => ({ ...prev, deleteDialogOpen: false, jobToDelete: null }))}
      >
        <DialogTitle>Delete Video</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this video? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setState(prev => ({ ...prev, deleteDialogOpen: false, jobToDelete: null }))}
          >
            Cancel
          </Button>
          <Button 
            onClick={confirmDelete} 
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VideoList;