import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useParams } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Home from './pages/Home';
import Dashboard from './pages/Dashboard';
import Library from './pages/Library';
import ContentCreation from './pages/ContentCreation';
import VideoDetails from './pages/VideoDetails';
import AudioCreator from './pages/AudioCreator';
import CodeExecutor from './pages/CodeExecutor';
import Images from './pages/Images';
import Documents from './pages/Documents';
import MediaTools from './pages/MediaTools';
import Upload from './pages/Upload';
import Simone from './pages/Simone';
import YtShorts from './pages/YtShorts';
import VideoTools from './pages/VideoTools';
import AIVideoTools from './pages/AIVideoTools';
import AIVideoGenerator from './pages/AIVideoGenerator';
import Users from './pages/Users';
import JobManagement from './pages/JobManagement';
import ApiKeys from './pages/ApiKeys';
import Settings from './pages/Settings';
import PollinationsAI from './pages/PollinationsAI';
import Feedback from './pages/Feedback';
import Layout from './components/Layout';
import { Box, CircularProgress } from '@mui/material';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const LoadingScreen: React.FC = () => (
  <Box 
    display="flex" 
    justifyContent="center" 
    alignItems="center" 
    minHeight="100vh"
    bgcolor="background.default"
  >
    <CircularProgress size={60} />
  </Box>
);

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

const VideoRedirect: React.FC = () => {
  const { videoId } = useParams<{ videoId: string }>();
  return <Navigate to={`/dashboard/library/${videoId}`} replace />;
};

const AppRoutes: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Routes>
      {/* Landing Page */}
      <Route 
        path="/" 
        element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <Home />} 
      />
      
      {/* Login Route (legacy compatibility) */}
      <Route 
        path="/login" 
        element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <Home />} 
      />
      
      {/* Dashboard Routes - New hierarchical structure */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <Layout>
              <Dashboard />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/library"
        element={
          <ProtectedRoute>
            <Layout>
              <Library />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/library/:videoId"
        element={
          <ProtectedRoute>
            <Layout>
              <VideoDetails />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/content-creation"
        element={
          <ProtectedRoute>
            <Layout>
              <ContentCreation />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/simone"
        element={
          <ProtectedRoute>
            <Layout>
              <Simone />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/audio"
        element={
          <ProtectedRoute>
            <Layout>
              <AudioCreator />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/code"
        element={
          <ProtectedRoute>
            <Layout>
              <CodeExecutor />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/images"
        element={
          <ProtectedRoute>
            <Layout>
              <Images />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/documents"
        element={
          <ProtectedRoute>
            <Layout>
              <Documents />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/media"
        element={
          <ProtectedRoute>
            <Layout>
              <MediaTools />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/upload"
        element={
          <ProtectedRoute>
            <Layout>
              <Upload />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/yt-shorts"
        element={
          <ProtectedRoute>
            <Layout>
              <YtShorts />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/video-tools"
        element={
          <ProtectedRoute>
            <Layout>
              <VideoTools />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/ai-video-tools"
        element={
          <ProtectedRoute>
            <Layout>
              <AIVideoTools />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/ai-video"
        element={
          <ProtectedRoute>
            <Layout>
              <AIVideoGenerator />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/pollinations"
        element={
          <ProtectedRoute>
            <Layout>
              <PollinationsAI />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/feedback"
        element={
          <ProtectedRoute>
            <Layout>
              <Feedback />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/jobs"
        element={
          <ProtectedRoute>
            <Layout>
              <JobManagement />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/admin/jobs"
        element={
          <ProtectedRoute>
            <Layout>
              <JobManagement />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      {/* Admin Routes */}
      <Route
        path="/dashboard/admin/users"
        element={
          <ProtectedRoute>
            <Layout>
              <Users />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/admin/api-keys"
        element={
          <ProtectedRoute>
            <Layout>
              <ApiKeys />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/admin/settings"
        element={
          <ProtectedRoute>
            <Layout>
              <Settings />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      <Route
        path="/dashboard/settings"
        element={
          <ProtectedRoute>
            <Layout>
              <Settings />
            </Layout>
          </ProtectedRoute>
        }
      />
      
      {/* Legacy Route Redirects for backward compatibility */}
      <Route path="/videos" element={<Navigate to="/dashboard/library" replace />} />
      <Route path="/library" element={<Navigate to="/dashboard/library" replace />} />
      <Route path="/create" element={<Navigate to="/dashboard/content-creation" replace />} />
      <Route path="/research" element={<Navigate to="/dashboard/content-creation" replace />} />
      <Route path="/dashboard/create" element={<Navigate to="/dashboard/content-creation" replace />} />
      <Route path="/dashboard/research" element={<Navigate to="/dashboard/content-creation" replace />} />
      <Route path="/video/:videoId" element={<VideoRedirect />} />
      <Route path="/jobs" element={<Navigate to="/dashboard/jobs" replace />} />
      <Route path="/users" element={<Navigate to="/dashboard/admin/users" replace />} />
      <Route path="/api-keys" element={<Navigate to="/dashboard/admin/api-keys" replace />} />
      <Route path="/settings" element={<Navigate to="/dashboard/settings" replace />} />
      
      {/* Catch all route */}
      <Route
        path="*"
        element={<Navigate to="/dashboard" replace />}
      />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <AuthProvider>
          <AppRoutes />
        </AuthProvider>
      </Router>
    </QueryClientProvider>
  );
};

export default App;
