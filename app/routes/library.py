"""
Library route for managing and browsing generated content.
Organizes content by type (audio, videos, images, etc.) with S3 direct access.
Uses persistent database storage instead of temporary job queue.
"""
from fastapi import APIRouter, Depends, Query, HTTPException
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from enum import Enum
from datetime import datetime
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload

from app.utils.auth import get_api_key
from app.services.s3 import s3_service
from app.database import database_service, MediaRecord, MediaType, MediaCategory, utcnow
from app.utils.logging import get_logger

# Initialize logger
logger = get_logger(module="routes", component="library")

router = APIRouter()

class ContentType(str, Enum):
    """Content type enumeration for library organization."""
    VIDEO = "video"
    AUDIO = "audio" 
    IMAGE = "image"
    TEXT = "text"
    ALL = "all"

class ContentItem(BaseModel):
    """Individual content item in the library."""
    job_id: str
    job_type: str
    content_type: ContentType
    title: Optional[str] = None
    description: Optional[str] = None
    file_url: str
    thumbnail_url: Optional[str] = None
    file_size: Optional[int] = None
    duration: Optional[float] = None  # For video/audio
    dimensions: Optional[Dict[str, int]] = None  # For images/videos {"width": 1920, "height": 1080}
    created_at: str
    updated_at: str
    metadata: Dict[str, Any] = {}
    parameters: Dict[str, Any] = {}

class LibraryResponse(BaseModel):
    """Library response with organized content."""
    content: List[ContentItem]
    total_count: int
    content_type_filter: ContentType
    pagination: Dict[str, Any]

# Mapping of MediaType to ContentType
MEDIA_TYPE_TO_CONTENT_TYPE = {
    'video': ContentType.VIDEO,
    'audio': ContentType.AUDIO,
    'image': ContentType.IMAGE,
    'document': ContentType.TEXT,
    'other': ContentType.TEXT,
}

def get_content_type_from_media_type(media_type) -> ContentType:
    """Map database media type to API content type."""
    # Handle both string and enum values
    if hasattr(media_type, 'value'):
        media_type_str = media_type.value
    else:
        media_type_str = str(media_type)
    
    return MEDIA_TYPE_TO_CONTENT_TYPE.get(media_type_str, ContentType.TEXT)

async def generate_presigned_url(s3_key: str) -> str:
    """Generate presigned URL for S3 content."""
    try:
        return await s3_service.generate_presigned_url(s3_key, expires_in=3600)
    except Exception as e:
        logger.error(f"Failed to generate presigned URL for {s3_key}: {e}")
        return ""

def extract_s3_key_from_url(s3_url: str) -> str:
    """Extract S3 key from S3 URL."""
    if s3_url.startswith("s3://"):
        # Remove s3:// prefix and bucket name
        parts = s3_url.replace("s3://", "").split("/", 1)
        if len(parts) > 1:
            return parts[1]
    elif s3_url.startswith("https://") and ".s3." in s3_url:
        # Handle HTTPS S3 URLs
        # Extract key from URL path
        from urllib.parse import urlparse
        parsed = urlparse(s3_url)
        return parsed.path.lstrip("/")
    return s3_url

@router.get("/content", response_model=LibraryResponse)
async def get_library_content(
    api_key: str = Depends(get_api_key),
    content_type: ContentType = Query(ContentType.ALL, description="Filter by content type"),
    limit: int = Query(50, ge=1, le=200, description="Number of items to return"),
    offset: int = Query(0, ge=0, description="Number of items to skip"),
    search: Optional[str] = Query(None, description="Search in titles and descriptions"),
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    date_from: Optional[str] = Query(None, description="Filter from date (ISO format)"),
    date_to: Optional[str] = Query(None, description="Filter to date (ISO format)")
):
    """
    Get library content organized by type with S3 direct access.
    Returns persistent media records that don't expire with jobs.
    """
    try:
        async for session in database_service.get_session():
            # Build base query
            query = select(MediaRecord).where(MediaRecord.is_deleted == False)
            
            # Apply content type filter using enum values
            if content_type != ContentType.ALL:
                if content_type == ContentType.VIDEO:
                    query = query.where(MediaRecord.media_type == MediaType.VIDEO.value)
                elif content_type == ContentType.AUDIO:
                    query = query.where(MediaRecord.media_type == MediaType.AUDIO.value)
                elif content_type == ContentType.IMAGE:
                    query = query.where(MediaRecord.media_type == MediaType.IMAGE.value)
                elif content_type == ContentType.TEXT:
                    query = query.where(MediaRecord.media_type.in_([MediaType.DOCUMENT.value, MediaType.OTHER.value]))
            
            # Apply search filter
            if search:
                search_term = f"%{search.lower()}%"
                query = query.where(
                    or_(
                        func.lower(MediaRecord.title).like(search_term),
                        func.lower(MediaRecord.description).like(search_term),
                        func.lower(MediaRecord.text_content).like(search_term),
                        func.lower(MediaRecord.prompt).like(search_term)
                    )
                )
            
            # Apply project filter
            if project_id:
                try:
                    project_id_int = int(project_id)
                    query = query.where(MediaRecord.project_id == project_id_int)
                except ValueError:
                    pass  # Invalid project ID, ignore filter
            
            # Apply date filters
            if date_from:
                try:
                    date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
                    query = query.where(MediaRecord.created_at >= date_from_dt)
                except ValueError:
                    pass  # Invalid date format, ignore filter
            
            if date_to:
                try:
                    date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
                    query = query.where(MediaRecord.created_at <= date_to_dt)
                except ValueError:
                    pass  # Invalid date format, ignore filter
            
            # Get total count
            count_query = select(func.count(MediaRecord.id)).where(query.whereclause)
            count_result = await session.execute(count_query)
            total_count = count_result.scalar()
            
            # Apply ordering and pagination
            query = query.order_by(desc(MediaRecord.created_at))
            query = query.offset(offset).limit(limit)
            
            # Execute query
            result = await session.execute(query)
            media_records = result.scalars().all()
            
            logger.info(f"Found {len(media_records)} media records from database")
            
            # Debug: Log the first few records if any exist
            if media_records:
                for i, record in enumerate(media_records[:3]):
                    logger.info(f"Record {i}: id={record.id}, media_type={record.media_type}, category={record.category}")
            else:
                logger.info("No media records found in database - table might be empty")
            
            # Convert to ContentItem objects
            content_items = []
            for record in media_records:
                try:
                    # Get presigned URL for the main file
                    file_url = record.primary_url
                    if file_url:
                        s3_key = extract_s3_key_from_url(file_url)
                        file_url = await generate_presigned_url(s3_key)
                    
                    # Get thumbnail URL if available
                    thumbnail_url = record.thumbnail_url
                    if thumbnail_url:
                        s3_key = extract_s3_key_from_url(thumbnail_url)
                        thumbnail_url = await generate_presigned_url(s3_key)
                    
                    # Build dimensions dict if available
                    dimensions = None
                    if record.dimensions and isinstance(record.dimensions, dict):
                        dimensions = record.dimensions
                    
                    # Convert file size from MB to bytes for consistency
                    file_size_bytes = None
                    if record.file_size_mb:
                        file_size_bytes = int(record.file_size_mb * 1024 * 1024)
                    
                    content_item = ContentItem(
                        job_id=record.id,
                        job_type=record.category.value if hasattr(record.category, 'value') else str(record.category),
                        content_type=get_content_type_from_media_type(record.media_type),
                        title=record.title,
                        description=record.description,
                        file_url=file_url,
                        thumbnail_url=thumbnail_url,
                        file_size=file_size_bytes,
                        duration=record.duration_seconds,
                        dimensions=dimensions,
                        created_at=record.created_at.isoformat(),
                        updated_at=record.updated_at.isoformat(),
                        metadata={
                            "format": record.format,
                            "provider": record.provider,
                            "model_used": record.model_used,
                            "language": record.language,
                            "word_count": record.word_count,
                            "processing_time": record.processing_time_seconds,
                            "download_count": record.download_count,
                            "view_count": record.view_count,
                            "is_favorite": record.is_favorite,
                            "secondary_urls": record.secondary_urls or {}
                        },
                        parameters=record.generation_params or {}
                    )
                    content_items.append(content_item)
                    
                except Exception as e:
                    logger.error(f"Error processing media record {record.id}: {e}")
                    continue
            
            # Prepare pagination info
            pagination = {
                "limit": limit,
                "offset": offset,
                "total_count": total_count,
                "has_next": offset + limit < total_count,
                "has_previous": offset > 0,
                "next_offset": offset + limit if offset + limit < total_count else None,
                "previous_offset": max(0, offset - limit) if offset > 0 else None
            }
            
            return LibraryResponse(
                content=content_items,
                total_count=total_count,
                content_type_filter=content_type,
                pagination=pagination
            )
        
    except Exception as e:
        logger.error(f"Error fetching library content: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch library content: {str(e)}")

@router.get("/content/{media_id}")
async def get_content_item(
    media_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get a specific content item by media ID."""
    try:
        async for session in database_service.get_session():
            # Query for the media record
            query = select(MediaRecord).where(
                and_(
                    MediaRecord.id == media_id,
                    MediaRecord.is_deleted == False
                )
            )
            
            result = await session.execute(query)
            record = result.scalar_one_or_none()
            
            if not record:
                raise HTTPException(status_code=404, detail="Content item not found")
            
            # Update view count
            record.view_count += 1
            record.last_accessed = utcnow()
            await session.commit()
            
            # Get presigned URLs
            file_url = record.primary_url
            if file_url:
                s3_key = extract_s3_key_from_url(file_url)
                file_url = await generate_presigned_url(s3_key)
            
            thumbnail_url = record.thumbnail_url
            if thumbnail_url:
                s3_key = extract_s3_key_from_url(thumbnail_url)
                thumbnail_url = await generate_presigned_url(s3_key)
            
            # Build dimensions dict if available
            dimensions = None
            if record.dimensions and isinstance(record.dimensions, dict):
                dimensions = record.dimensions
            
            # Convert file size from MB to bytes for consistency
            file_size_bytes = None
            if record.file_size_mb:
                file_size_bytes = int(record.file_size_mb * 1024 * 1024)
            
            content_item = ContentItem(
                job_id=record.id,
                job_type=record.category.value if hasattr(record.category, 'value') else str(record.category),
                content_type=get_content_type_from_media_type(record.media_type),
                title=record.title,
                description=record.description,
                file_url=file_url,
                thumbnail_url=thumbnail_url,
                file_size=file_size_bytes,
                duration=record.duration_seconds,
                dimensions=dimensions,
                created_at=record.created_at.isoformat(),
                updated_at=record.updated_at.isoformat(),
                metadata={
                    "format": record.format,
                    "provider": record.provider,
                    "model_used": record.model_used,
                    "language": record.language,
                    "word_count": record.word_count,
                    "processing_time": record.processing_time_seconds,
                    "download_count": record.download_count,
                    "view_count": record.view_count,
                    "is_favorite": record.is_favorite,
                    "secondary_urls": record.secondary_urls or {}
                },
                parameters=record.generation_params or {}
            )
            
            return content_item
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching content item {media_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch content item: {str(e)}")

@router.get("/stats")
async def get_library_stats(api_key: str = Depends(get_api_key)):
    """Get library statistics grouped by content type."""
    try:
        async for session in database_service.get_session():
            # Base query for non-deleted items
            base_query = select(func.count(MediaRecord.id)).where(MediaRecord.is_deleted == False)
            
            # Get counts by media type using enum values
            video_count_query = base_query.where(MediaRecord.media_type == MediaType.VIDEO.value)
            video_result = await session.execute(video_count_query)
            video_count = video_result.scalar()
            
            audio_count_query = base_query.where(MediaRecord.media_type == MediaType.AUDIO.value)
            audio_result = await session.execute(audio_count_query)
            audio_count = audio_result.scalar()
            
            image_count_query = base_query.where(MediaRecord.media_type == MediaType.IMAGE.value)
            image_result = await session.execute(image_count_query)
            image_count = image_result.scalar()
            
            text_count_query = base_query.where(MediaRecord.media_type.in_([MediaType.DOCUMENT.value, MediaType.OTHER.value]))
            text_result = await session.execute(text_count_query)
            text_count = text_result.scalar()
            
            # Get total count
            total_query = select(func.count(MediaRecord.id)).where(MediaRecord.is_deleted == False)
            total_result = await session.execute(total_query)
            total_count = total_result.scalar()
            
            stats = {
                ContentType.VIDEO.value: video_count,
                ContentType.AUDIO.value: audio_count,
                ContentType.IMAGE.value: image_count,
                ContentType.TEXT.value: text_count,
                "total": total_count
            }
            
            return {
                "stats": stats,
                "total_items": total_count
            }
        
    except Exception as e:
        logger.error(f"Error fetching library stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch library stats: {str(e)}")

@router.post("/scan-s3")
async def scan_s3_for_content(api_key: str = Depends(get_api_key)):
    """
    Scan S3 bucket for content that might not be in the database.
    This is a utility endpoint for discovering existing content.
    """
    try:
        # Basic S3 bucket scan
        bucket_contents = await s3_service.list_objects("")
        
        if not bucket_contents:
            return {
                "message": "No content found in S3 bucket",
                "files_found": 0,
                "already_in_library": 0,
                "new_discoveries": 0
            }
        
        async for session in database_service.get_session():
            # Get existing media record IDs
            existing_query = select(MediaRecord.id).where(MediaRecord.is_deleted == False)
            result = await session.execute(existing_query)
            existing_ids = {row[0] for row in result}
            
            files_found = len(bucket_contents)
            already_in_library = 0
            new_discoveries = []
            
            for obj in bucket_contents:
                key = obj.get('Key', '')
                
                # Extract potential job ID from S3 key path
                # Assuming structure like: "jobs/job-uuid/output.mp4"
                if '/jobs/' in key or '/media/' in key:
                    path_parts = key.split('/')
                    potential_job_id = None
                    
                    for part in path_parts:
                        # Look for UUID-like strings
                        if len(part) >= 32 and '-' in part:
                            potential_job_id = part
                            break
                    
                    if potential_job_id:
                        if potential_job_id in existing_ids:
                            already_in_library += 1
                        else:
                            new_discoveries.append({
                                "s3_key": key,
                                "potential_job_id": potential_job_id,
                                "file_size": obj.get('Size', 0),
                                "last_modified": obj.get('LastModified', '').isoformat() if obj.get('LastModified') else None
                            })
            
            return {
                "message": f"S3 scan completed. Found {files_found} files in bucket.",
                "files_found": files_found,
                "already_in_library": already_in_library,
                "new_discoveries": len(new_discoveries),
                "discovery_details": new_discoveries[:50]  # Limit to first 50 for response size
            }
        
    except Exception as e:
        logger.error(f"Error scanning S3 for content: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to scan S3: {str(e)}")

@router.post("/favorite/{media_id}")
async def toggle_favorite(
    media_id: str,
    api_key: str = Depends(get_api_key)
):
    """Toggle favorite status for a media item."""
    try:
        async for session in database_service.get_session():
            query = select(MediaRecord).where(
                and_(
                    MediaRecord.id == media_id,
                    MediaRecord.is_deleted == False
                )
            )
            
            result = await session.execute(query)
            record = result.scalar_one_or_none()
            
            if not record:
                raise HTTPException(status_code=404, detail="Content item not found")
            
            # Toggle favorite status
            record.is_favorite = not record.is_favorite
            record.updated_at = utcnow()
            
            await session.commit()
            
            return {
                "media_id": media_id,
                "is_favorite": record.is_favorite,
                "message": f"Item {'added to' if record.is_favorite else 'removed from'} favorites"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling favorite for {media_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to toggle favorite: {str(e)}")

@router.delete("/content/{media_id}")
async def delete_content_item(
    media_id: str,
    api_key: str = Depends(get_api_key)
):
    """Soft delete a content item (marks as deleted, doesn't remove from S3)."""
    try:
        async for session in database_service.get_session():
            query = select(MediaRecord).where(
                and_(
                    MediaRecord.id == media_id,
                    MediaRecord.is_deleted == False
                )
            )
            
            result = await session.execute(query)
            record = result.scalar_one_or_none()
            
            if not record:
                raise HTTPException(status_code=404, detail="Content item not found")
            
            # Soft delete
            record.is_deleted = True
            record.updated_at = utcnow()
            
            await session.commit()
            
            return {
                "media_id": media_id,
                "message": "Content item deleted successfully"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting content item {media_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete content item: {str(e)}")

@router.get("/debug/status")
async def get_library_debug_status():
    """Debug endpoint to check library database status."""
    try:
        async for session in database_service.get_session():
            # Check if table exists and get basic stats
            try:
                total_count_query = select(func.count(MediaRecord.id))
                total_result = await session.execute(total_count_query)
                total_count = total_result.scalar()
                
                deleted_count_query = select(func.count(MediaRecord.id)).where(MediaRecord.is_deleted == True)
                deleted_result = await session.execute(deleted_count_query)
                deleted_count = deleted_result.scalar()
                
                # Get a sample record if any exist
                sample_query = select(MediaRecord).limit(1)
                sample_result = await session.execute(sample_query)
                sample_record = sample_result.scalar_one_or_none()
                
                sample_data = None
                if sample_record:
                    sample_data = {
                        "id": sample_record.id,
                        "title": sample_record.title,
                        "media_type": str(sample_record.media_type),
                        "category": str(sample_record.category),
                        "primary_url": sample_record.primary_url,
                        "created_at": sample_record.created_at.isoformat()
                    }
                
                return {
                    "status": "ok",
                    "table_exists": True,
                    "total_records": total_count,
                    "active_records": total_count - deleted_count,
                    "deleted_records": deleted_count,
                    "sample_record": sample_data
                }
                
            except Exception as e:
                logger.error(f"Database query error: {e}")
                return {
                    "status": "error",
                    "table_exists": False,
                    "error": str(e)
                }
                
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")