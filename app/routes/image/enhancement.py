"""
Routes for image enhancement and artifact removal (unaize) operations.
"""
import uuid
import logging
from typing import Any
from fastapi import APIRouter, HTTPException
from app.models import (
    ImageEnhancementRequest, 
    JobResponse, 
    JobStatusResponse,
    JobType
)
from app.services.job_queue import job_queue
from app.services.image.enhancement_service import image_enhancement_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/images", tags=["images"])


@router.post("/enhance", response_model=JobResponse)
async def create_image_enhancement_job(request: ImageEnhancementRequest):
    """
    Create a job to enhance an image by removing AI artifacts and adding natural imperfections.
    
    This endpoint processes a request to enhance images by:
    - Removing AI-generated artifacts and over-smoothing
    - Adding natural imperfections like film grain and noise
    - Adjusting color saturation and contrast for more authentic look
    - Applying vintage/analog effects for organic appearance
    - Making AI-generated images look more natural and less "perfect"
    
    Args:
        request: Image enhancement request with the following parameters:
            - image_url: URL of the image to enhance
            - enhance_color: Color enhancement strength (0.0-2.0, default: 1.0)
            - enhance_contrast: Contrast enhancement strength (0.0-2.0, default: 1.0)
            - noise_strength: Noise/grain strength (0-100, default: 10)
            - remove_artifacts: Apply AI artifact removal (default: True)
            - add_film_grain: Add film grain effect (default: False)
            - vintage_effect: Vintage/analog effect strength (0.0-1.0, default: 0.0)
            - output_format: Output format (default: 'png')
            - output_quality: Quality for lossy formats (1-100, default: 90)
            
    Returns:
        JobResponse with job_id that can be used to check the status of the job
    """
    try:
        # Create parameters dictionary
        params = {
            "image_url": str(request.image_url),
            "enhance_color": request.enhance_color,
            "enhance_contrast": request.enhance_contrast,
            "noise_strength": request.noise_strength,
            "remove_artifacts": request.remove_artifacts,
            "add_film_grain": request.add_film_grain,
            "vintage_effect": request.vintage_effect,
            "output_format": request.output_format,
            "output_quality": request.output_quality
        }
        
        # Create and start the job using new job queue
        job_id = str(uuid.uuid4())
        
        # Create a wrapper function that matches the expected signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await image_enhancement_service.enhance_image(data)
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.IMAGE_ENHANCEMENT,
            process_func=process_wrapper,
            data=params
        )
        
        logger.info(f"Created image enhancement job: {job_id}")
        
        return JobResponse(job_id=job_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/enhance/{job_id}", response_model=JobStatusResponse)
async def get_image_enhancement_job_status(job_id: str):
    """
    Get the status of an image enhancement job.
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing the job status and results when completed
    """
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )