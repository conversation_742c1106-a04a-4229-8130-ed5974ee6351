import os
import json
import re
import logging
from typing import Dict, <PERSON>, <PERSON><PERSON>, <PERSON>
from openai import OpenAI
from app.utils.ai_context import get_current_context

logger = logging.getLogger(__name__)

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    Groq = None
    GROQ_AVAILABLE = False

try:
    from .news_research_service import news_research_service
    NEWS_RESEARCH_AVAILABLE = True
except ImportError:
    news_research_service = None
    NEWS_RESEARCH_AVAILABLE = False


class AIScriptGenerator:
    """AI scr        # Make the API call
        logger.info(f"Making API request with model: {model}, provider: {actual_provider}")
        logger.debug(f"System prompt length: {len(prompt)}, User content length: {len(user_content)}")
        logger.debug(f"Topic: '{topic}', Script type: '{script_type}', Language: '{language}'")
        logger.debug(f"User content type: {type(user_content)}")  # Add type debugginggeneration service supporting multiple providers."""
    
    def __init__(self):
        self.openai_client = None
        self.groq_client = None
        self._setup_clients()
    
    def _setup_clients(self):
        """Initialize AI clients based on available API keys."""
        # Setup OpenAI client
        openai_key = os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_KEY')
        if openai_key:
            # Get base URL from environment (for OpenAI-compatible LLMs)
            openai_base_url = os.getenv('OPENAI_BASE_URL')
            if openai_base_url:
                self.openai_client = OpenAI(api_key=openai_key, base_url=openai_base_url)
            else:
                self.openai_client = OpenAI(api_key=openai_key)
        
        # Setup Groq client
        groq_key = os.getenv('GROQ_API_KEY')
        if groq_key and GROQ_AVAILABLE and Groq and len(groq_key) > 30:
            groq_base_url = os.getenv('GROQ_BASE_URL')
            if groq_base_url:
                self.groq_client = Groq(api_key=groq_key, base_url=groq_base_url)
            else:
                self.groq_client = Groq(api_key=groq_key)
    
    def _get_provider_and_model(self, provider: str) -> Tuple[Union[OpenAI, Any], str, str]:
        """Get the appropriate client and model based on provider preference."""
        # Get configurable model names from environment
        openai_model = os.getenv('OPENAI_MODEL', 'gpt-4o')
        groq_model = os.getenv('GROQ_MODEL', 'mixtral-8x7b-32768')
        
        if provider == "groq" and self.groq_client:
            return self.groq_client, groq_model, "groq"
        elif provider == "openai" and self.openai_client:
            return self.openai_client, openai_model, "openai"
        elif provider == "auto":
            # Auto-select based on availability (prefer Groq if available)
            if self.groq_client:
                return self.groq_client, groq_model, "groq"
            elif self.openai_client:
                return self.openai_client, openai_model, "openai"
        else:
            # Fallback to any available client
            if self.openai_client:
                return self.openai_client, openai_model, "openai"
            elif self.groq_client:
                return self.groq_client, groq_model, "groq"
        
        raise ValueError("No AI provider available. Please set OPENAI_API_KEY or GROQ_API_KEY environment variable.")
    
    def _get_script_prompt(self, script_type: str, max_duration: int, target_words: int, language: str = "english") -> str:
        """Generate enhanced prompts for natural speech patterns and conversational scripts."""
        language_name = language.title() if language.lower() != "english" else "English"
        language_instruction = f" Write the entire script in {language_name}." if language.lower() != "english" else ""
        
        # Enhanced conversational writing rules based on TypeScript ResearchService
        base_intro = f"""You are an expert voice-over script writer who creates scripts that sound completely natural when spoken aloud. You avoid written text patterns and focus on conversational, spoken language.

CRITICAL TOPIC ADHERENCE RULES:
1. STAY STRICTLY ON THE PROVIDED TOPIC - Do not deviate or introduce unrelated content
2. Every sentence must directly relate to the main topic provided by the user
3. If the topic is specific (e.g., "ocean facts"), focus ONLY on that subject
4. Do not add generic introductions or conclusions that don't relate to the topic
5. Make every word count toward explaining or exploring the specific topic

CRITICAL CONVERSATIONAL WRITING RULES:
1. Write text that sounds NATURAL when spoken aloud by a voice-over artist
2. Use contractions (don't, can't, it's, we're) to sound more natural
3. Avoid written text phrases like "this video", "we will explore", "in this section"
4. Write as if you're talking directly to someone, not presenting information
5. Use spoken language patterns: "You know what's crazy?", "Here's the thing...", "But wait..."
6. Include natural pauses and emphasis through sentence structure
7. Make it sound like casual, engaging conversation - NOT formal writing{language_instruction}

SPEECH PATTERN EXAMPLES:
❌ BAD (Written style): "This video will show you {script_type}..."
✅ GOOD (Spoken style): "You won't believe what I just discovered about {script_type}..."

TARGET: {target_words} words (approximately {max_duration} seconds when spoken naturally)

IMPORTANT: The user will provide a specific topic. Your entire script must be about that exact topic. Do not include generic content, introductions, or conclusions that don't directly relate to the user's topic.

{get_current_context()}"""
        
        if script_type == "facts":
            example = """
FACTS SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "You won't believe this, but bananas are actually berries while strawberries aren't. Crazy, right? And here's something that'll blow your mind - a single cloud weighs over a million pounds. That's like having 100 elephants floating above your head!"

✅ GOOD: "Did you know honey never spoils? Archaeologists found 3,000-year-old honey in Egyptian tombs that's still perfectly edible. But wait, there's more - octopuses have three hearts and blue blood. Nature's basically an alien planet!"

Focus on:
- Hook with surprise: "You won't believe this..." "Here's something crazy..."
- Natural transitions: "But here's what's even weirder..." "And get this..."
- Make facts relatable: "That's like..." comparisons
- Conversational excitement: "Crazy, right?" "I know, right?"
        """
        elif script_type == "story":
            example = """
STORY SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "This really happened, and it's absolutely insane. A man disappears for 11 years, then shows up at his own funeral. Everyone's crying, his wife's devastated, and then he just walks through the door like nothing happened."

✅ GOOD: "Picture this - you're walking down the street when suddenly a stranger hands you a key. No words, just a key. That's exactly what happened to Sarah, and what she found will haunt you."

Focus on:
- Immediate drama: "This really happened..." "You won't believe what..."
- Present tense for urgency: "He's walking..." "She opens the door..."
- Natural storytelling: "Picture this..." "So get this..."
- Emotional hooks: "It'll haunt you" "You'll never see it coming"
        """
        elif script_type == "educational":
            example = """
EDUCATIONAL SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "Your brain is basically a computer, and I'm about to show you how to upgrade its software. This simple trick will literally change how you learn forever, and it takes just 30 seconds."

✅ GOOD: "Ever wonder why some people remember everything? It's not talent - it's technique. Here's the secret method that memory champions don't want you to know."

Focus on:
- Practical benefit hook: "This will save you..." "I'm about to show you..."
- Simple analogies: "Think of it like..." "It's basically..."
- Make learning effortless: "All you do is..." "It's that simple"
- Promise transformation: "This will change..." "You'll never..."
        """
        elif script_type == "motivation":
            example = """
MOTIVATIONAL SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "Stop telling yourself you'll start tomorrow. The life you want begins with what you do right now. Every single champion was once exactly where you are - scared, uncertain, but they did it anyway."

✅ GOOD: "You know what separates winners from everyone else? It's not talent, it's not luck - it's the moment they stopped making excuses. That moment is right now, and you're in control."

Focus on:
- Direct address: "You", "Your life", "Your dreams"
- Relatable struggle: "We've all been there..." "You know that feeling..."
- Urgent empowerment: "Right now", "This moment", "You have the power"
- Strong call-to-action: "Stop making excuses", "Take control", "You decide"
        """
        elif script_type == "prayer":
            example = """
PRAYER/SPIRITUAL SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "Take a deep breath with me. In this moment, you're exactly where you need to be. Let go of today's worries and feel the peace that's always been inside you."

✅ GOOD: "You've been carrying so much, haven't you? It's okay to rest now. May your heart find the quiet strength it needs, and may tomorrow bring you gentle hope."

Focus on:
- Gentle, personal address: "Take a breath..." "You've been..."
- Present moment peace: "Right now..." "In this moment..."
- Comforting reassurance: "It's okay..." "You're safe..."
- Natural spiritual language: avoiding formal religious phrases
        """
        elif script_type == "pov":
            example = """
POV SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "POV: You're texting your crush and they're typing for like 10 minutes. Your heart's racing, you're overthinking everything, and then they just send 'k'. The audacity!"

✅ GOOD: "POV: You're home alone and hear someone call your name. But here's the thing - you live alone. Your blood turns cold because you realize... someone just used your actual name."

Focus on:
- Immediate immersion: "POV: You're..." "Picture this..."
- Relatable emotions: "Your heart's racing..." "You're overthinking..."
- Natural reactions: "The audacity!" "Your blood turns cold..."
- Cliffhanger endings: "But here's the thing..." "You realize..."
        """
        elif script_type == "conspiracy":
            example = """
CONSPIRACY/MYSTERY SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "They just declassified documents from 1947, and what they found will make your blood run cold. The government's been hiding this for 75 years, but now we finally know the truth."

✅ GOOD: "You know that thing you were taught in school? Yeah, that's not what really happened. What I'm about to show you will make you question everything you thought you knew."

Focus on:
- Immediate mystery: "They just declassified..." "You know that thing..."
- Personal stakes: "will make your blood run cold" "you were taught"
- Shocking revelations: "finally know the truth" "question everything"
- Conspiratorial tone: "they've been hiding" "what really happened"
        """
        elif script_type == "life_hacks":
            example = """
LIFE HACKS SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "I've been doing this wrong my entire life! Put a wet paper towel under your cutting board and it'll never slide around again. This literally takes 2 seconds and will save you so much frustration."

✅ GOOD: "Why didn't anyone tell me this sooner? If you're struggling to open jars, just wrap a rubber band around the lid. I'm not even kidding - it works every single time."

Focus on:
- Personal revelation: "I've been doing this wrong..." "Why didn't anyone tell me..."
- Instant solution: "literally takes 2 seconds" "works every single time"
- Relatable frustration: "save you frustration" "struggling with"
- Enthusiastic discovery: "I'm not even kidding!" "You won't believe this!"
        """
        elif script_type == "would_you_rather":
            example = """
WOULD YOU RATHER SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "Okay, this is brutal. Would you rather know exactly when you're going to die, or how you're going to die? Think about it - knowing when means you can plan everything perfectly, but knowing how? That's just pure anxiety."

✅ GOOD: "Here's one that'll mess with your head. Would you rather read everyone's thoughts but never turn it off, or be invisible but only when literally no one's looking? Both sound amazing until you really think about it..."

Focus on:
- Engaging setup: "Okay, this is brutal..." "Here's one that'll mess with your head..."
- Impossible dilemmas: both options equally appealing/terrifying
- Thinking process: "Think about it..." "sounds amazing until..."
- Viewer engagement: encouraging comments and discussion
        """
        elif script_type == "before_you_die":
            example = """
BEFORE YOU DIE SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "Life's too short to keep putting this off. There are five experiences that'll change you forever, and most people never have them. Don't be most people."

✅ GOOD: "You know what you'll regret on your deathbed? Not the things you did, but the things you were too scared to try. Here's what you absolutely can't miss out on..."

Focus on:
- Life urgency: "Life's too short..." "You know what you'll regret..."
- FOMO motivation: "most people never" "don't be most people"
- Personal transformation: "change you forever" "absolutely can't miss"
- Inspiring action: "stop putting this off" "too scared to try"
        """
        elif script_type == "dark_psychology":
            example = """
DARK PSYCHOLOGY SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "Someone's been using this trick on you your whole life, and you had no idea. It's called the foot-in-the-door technique, and once you see it, you can't unsee it."

✅ GOOD: "Ever notice how some people just seem to get whatever they want? It's not charm - it's psychology. Here are the three tactics they're using that you need to know about."

Focus on:
- Awareness revelation: "you had no idea" "once you see it, you can't unsee it"
- Educational protection: teaching defense, not offense
- Psychological insight: "It's not charm - it's psychology"
- Practical recognition: "here's what to look for" "you need to know"
        """
        elif script_type == "reddit_stories":
            example = """
REDDIT STORIES SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "So this actually happened to me last week, and I'm still processing it. I found out my roommate's been stealing my food for months. What I did next? Well, let's just say karma works in mysterious ways."

✅ GOOD: "You guys, I need to tell someone about this because it's been eating me alive. My best friend asked me to be her maid of honor, but here's the thing - she's marrying my ex."

Focus on:
- Personal authenticity: "this actually happened to me" "I need to tell someone"
- Emotional investment: "I'm still processing" "eating me alive"
- Relatable drama: relationship conflicts, workplace drama
- Engaging cliffhangers: "What I did next?" "here's the thing"
        """
        elif script_type == "shower_thoughts":
            example = """
SHOWER THOUGHTS SCRIPT STYLE - Natural Speech Examples:
✅ GOOD: "Wait, hear me out on this one. If you're waiting for the waiter at a restaurant, doesn't that technically make you the waiter? Like, who's really serving who here?"

✅ GOOD: "I just realized something that's going to mess with your head. Every single photo of you is from the past. You've literally never seen yourself in real time. Wild, right?"

Focus on:
- Mind-bending setup: "hear me out on this" "I just realized something"
- Philosophical questions: "who's really serving who?" "literally never seen yourself"  
- Revelation moments: "that's going to mess with your head" "Wild, right?"
- Thought experiments: making the ordinary seem extraordinary
        """
        elif script_type == "daily_news":
            example = """
        For daily news content, create informative and engaging news scripts with:
        - Current events and breaking news updates
        - Key facts and important developments
        - Clear, objective reporting style
        - Context and background information
        - Impact on viewers' daily lives
        - Credible sources and recent information
        - Professional news delivery tone
        Example: "Breaking: Major breakthrough in renewable energy as scientists achieve 47% solar panel efficiency. This could reduce your electricity bills by 60% within 5 years. Here's what this means for you..."
        """
        else:
            example = ""
        
        return f"""{base_intro}
        {example}
        
        You are now tasked with creating the best short script based on the user's requested topic.

        SCRIPT REQUIREMENTS:
        1. Create a COHESIVE, FLOWING script - not fragmented sentences
        2. Stay 100% focused on the user's specific topic throughout
        3. Make it engaging and conversational from start to finish
        4. Aim for approximately {target_words} words to fit within {max_duration} seconds
        5. Create content that works as ONE continuous narration, not separate scenes
        6. Include natural transitions between ideas within the topic

        IMPORTANT: Write the script as one flowing piece of content that tells a complete story or explanation about the topic. Do not write disconnected facts or sentences.

        CRITICAL JSON OUTPUT RULES:
        1. ONLY output a valid JSON object - no extra text before or after
        2. The "script" field must contain ONLY the spoken content - NO JSON syntax, brackets, or quotes
        3. Do NOT include any JSON formatting inside the script text itself
        4. The script should be pure conversational text that sounds natural when spoken aloud

        # Output Format (EXACT FORMAT REQUIRED)
        {{
            "title": "Catchy video title (max 60 characters)",
            "description": "Brief engaging description (max 150 characters)",
            "script": "The actual script content for TTS narration - write as one flowing, cohesive piece"
        }}
        """
    
    def _extract_structured_response(self, content: str) -> Dict[str, str]:
        """Extract structured response from AI response with robust error handling."""
        # Clean the content first - remove any non-JSON text before and after
        content = content.strip()
        
        try:
            # Try direct JSON parsing first
            parsed = json.loads(content)
            if "title" in parsed and "description" in parsed and "script" in parsed:
                # Clean the script to ensure it doesn't contain JSON artifacts
                clean_script = self._remove_json_artifacts(parsed["script"])
                return {
                    "title": parsed["title"],
                    "description": parsed["description"], 
                    "script": clean_script
                }
        except (json.JSONDecodeError, KeyError):
            pass
        
        try:
            # Try to find JSON object in the response
            json_start = content.find('{')
            json_end = content.rfind('}')
            if json_start != -1 and json_end != -1:
                json_content = content[json_start:json_end+1]
                parsed = json.loads(json_content)
                if "title" in parsed and "description" in parsed and "script" in parsed:
                    # Clean the script to ensure it doesn't contain JSON artifacts
                    clean_script = self._remove_json_artifacts(parsed["script"])
                    return {
                        "title": parsed["title"],
                        "description": parsed["description"], 
                        "script": clean_script
                    }
        except (json.JSONDecodeError, KeyError):
            pass
        
        # Fallback: try to extract from structured format (old format)
        try:
            title_match = re.search(r'TITRE\s*:\s*[«"]*([^»"\n]+)[»"]*', content, re.IGNORECASE)
            description_match = re.search(r'DESCRIPTION\s*:\s*[«"]*([^»"\n]+)[»"]*', content, re.IGNORECASE)
            script_match = re.search(r'SCRIPT\s*:\s*\n?(.*)', content, re.DOTALL | re.IGNORECASE)
            
            if script_match:
                return {
                    "title": title_match.group(1).strip() if title_match else "Generated Video",
                    "description": description_match.group(1).strip() if description_match else "AI-generated content",
                    "script": script_match.group(1).strip()
                }
        except Exception:
            pass
        
        # If all else fails, treat entire content as script
        cleaned_script = self._clean_conversational_script(content.strip())
        # Remove any JSON artifacts from the fallback script
        cleaned_script = self._remove_json_artifacts(cleaned_script)
        return {
            "title": "Generated Video",
            "description": "AI-generated content", 
            "script": cleaned_script
        }
    
    def _clean_conversational_script(self, script: str) -> str:
        """Clean script to ensure it follows conversational speech patterns."""
        import re
        
        # Remove common written text patterns that don't sound natural when spoken
        patterns_to_remove = [
            r'\b(this video|this content|in this video|in this section)\b',
            r'\b(we will explore|we will examine|we will discuss)\b',
            r'\b(let us|let\'s explore|let\'s examine)\b',
            r'\b(today we|today I will)\b',
            r'\b(welcome to|thanks for watching)\b'
        ]
        
        cleaned = script
        for pattern in patterns_to_remove:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # Fix common issues
        cleaned = re.sub(r'\s+', ' ', cleaned)  # Multiple spaces
        cleaned = re.sub(r'^\s*[,\.]\s*', '', cleaned)  # Leading punctuation
        cleaned = cleaned.strip()
        
        # Ensure it starts with an engaging hook if it doesn't already
        if cleaned and not any(cleaned.lower().startswith(hook) for hook in [
            "you", "did you know", "here's", "imagine", "picture this", 
            "what if", "ever wonder", "you won't believe", "this is"
        ]):
            # Add a conversational hook based on content
            if "fact" in cleaned.lower() or "know" in cleaned.lower():
                cleaned = f"You won't believe this, but {cleaned.lower()}"
            elif "story" in cleaned.lower() or "happened" in cleaned.lower():
                cleaned = f"This is absolutely wild. {cleaned}"
            else:
                cleaned = f"Here's something crazy - {cleaned.lower()}"
        
        return cleaned
    
    def _remove_json_artifacts(self, script: str) -> str:
        """Remove JSON artifacts and malformed JSON from script content."""
        import re
        
        # Remove JSON-like patterns that might appear in script
        # Remove curly braces with content that looks like JSON
        script = re.sub(r'\{[^}]*"[^"]*"[^}]*\}', '', script)
        
        # Remove standalone JSON keys like "title":", "description":", "script":
        script = re.sub(r'["\']?(?:title|description|script)["\']?\s*:', '', script, flags=re.IGNORECASE)
        
        # Remove JSON brackets and quotes at start/end
        script = re.sub(r'^[\s\{\["\'\_\-]*', '', script)
        script = re.sub(r'[\s\}\]"\'\_\-]*$', '', script)
        
        # Remove escaped quotes and newlines
        script = script.replace('\\n', ' ').replace('\\"', '"').replace("\\", "")
        
        # Remove multiple spaces and clean up
        script = re.sub(r'\s+', ' ', script).strip()
        
        return script
    
    def _count_words(self, text: str) -> int:
        """Count words in text."""
        return len(text.split())
    
    def _estimate_duration(self, word_count: int, speaking_rate: float = 2.8, 
                          tts_speed: float = 1.0, script_type: str = "facts") -> float:
        """
        Estimate duration based on word count, speaking rate, TTS speed, and script type.
        
        Args:
            word_count: Number of words in script
            speaking_rate: Base words per second (default 2.8)
            tts_speed: TTS speed multiplier (0.5-2.0, default 1.0)
            script_type: Type of script affecting pacing
        """
        # Adjust base speaking rate for script type
        script_adjustments = {
            "conspiracy": 0.7,      # More dramatic pauses, slower delivery
            "story": 0.8,           # Narrative pacing with pauses
            "motivation": 0.75,     # Emphasis and inspiration pauses
            "dark_psychology": 0.7, # Dramatic, slower delivery
            "prayer": 0.6,          # Very slow, reverent pace
            "facts": 1.0,           # Standard pace
            "educational": 0.9,     # Slightly slower for clarity
            "life_hacks": 1.1,      # Faster, energetic pace
            "would_you_rather": 0.8 # Pauses for thinking
        }
        
        # Apply script type adjustment
        script_multiplier = script_adjustments.get(script_type, 1.0)
        adjusted_rate = speaking_rate * script_multiplier
        
        # Apply TTS speed (inverse relationship - slower speed = longer duration)
        speed_adjusted_rate = adjusted_rate * tts_speed
        
        # Calculate base duration
        base_duration = word_count / speed_adjusted_rate
        
        # Add extra time for punctuation and natural pauses
        # Estimate 0.3s per sentence (rough count by periods/exclamations)
        estimated_sentences = max(1, word_count // 12)  # ~12 words per sentence
        pause_time = estimated_sentences * 0.3
        
        total_duration = base_duration + pause_time
        
        return total_duration
    
    async def generate_script(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an AI script based on the provided parameters.
        
        Args:
            params: Dictionary containing:
                - topic: The topic for script generation
                - provider: AI provider preference ('auto', 'openai', 'groq')
                - script_type: Type of script ('facts', 'story', 'educational')
                - max_duration: Maximum duration in seconds
                - target_words: Target word count
                - language: Output language (default: 'english')
        
        Returns:
            Dictionary containing the generated script and metadata
        """
        topic = params.get('topic')
        provider = params.get('provider', 'auto')
        script_type = params.get('script_type', 'facts')
        max_duration = params.get('max_duration', 60)
        # Calculate target words based on duration if not provided
        if 'target_words' not in params:
            target_words = max(200, int(max_duration * 2.8))  # 2.8 words per second
        else:
            target_words = params.get('target_words', 200)
        language = params.get('language', 'english')
        
        if not topic:
            raise ValueError("Topic is required for script generation")
        
        # Validate and ensure topic is a string
        if not isinstance(topic, str):
            if isinstance(topic, dict) and 'topic' in topic:
                topic = topic['topic']
            else:
                topic = str(topic)
        if not topic.strip():
            raise ValueError("Topic cannot be empty")
        
        logger.debug(f"Validated topic: '{topic}' (type: {type(topic)})")
        
        # Get the appropriate client and model
        client, model, actual_provider = self._get_provider_and_model(provider)
        
        # For daily news scripts, research current information
        news_context = ""
        if script_type == "daily_news" and NEWS_RESEARCH_AVAILABLE and news_research_service:
            try:
                research_results = await news_research_service.research_topic(topic)
                if research_results.get('summary'):
                    news_context = f"\n\nCurrent news context:\n{research_results['summary']}"
            except Exception as e:
                logger.warning(f"News research failed, proceeding without context: {e}")
        
        # Generate the prompt with language support
        prompt = self._get_script_prompt(script_type, max_duration, target_words, language)
        
        # Combine topic with news context for daily_news scripts
        user_content = topic + news_context if news_context else topic
        
        # Ensure user_content is a string (final safety check)
        if not isinstance(user_content, str):
            user_content = str(user_content)
        
        # Make the API call
        logger.info(f"Making API request with model: {model}, provider: {actual_provider}")
        logger.debug(f"System prompt length: {len(prompt)}, User content length: {len(user_content)}")
        logger.debug(f"Topic: '{topic}', Script type: '{script_type}', Language: '{language}'")
        
        # Check for potentially problematic content
        if len(user_content) > 8000:
            logger.warning(f"User content is very long ({len(user_content)} chars), might cause API issues")
        if len(prompt) > 8000:
            logger.warning(f"System prompt is very long ({len(prompt)} chars), might cause API issues")
        
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": user_content}
                ]
            )
        except Exception as e:
            logger.error(f"API request failed with model '{model}' and provider '{actual_provider}': {e}")
            
            # If using OpenRouter and the primary model fails, try a fallback
            openai_base_url = os.getenv('OPENAI_BASE_URL', '')
            if 'openrouter.ai' in openai_base_url and model == 'tngtech/deepseek-r1t2-chimera:free':
                logger.warning(f"Primary model {model} failed, trying fallback model...")
                try:
                    fallback_model = 'meta-llama/llama-3.2-3b-instruct:free'
                    logger.info(f"Retrying with fallback model: {fallback_model}")
                    response = client.chat.completions.create(
                        model=fallback_model,
                        messages=[
                            {"role": "system", "content": prompt},
                            {"role": "user", "content": user_content}
                        ]
                    )
                except Exception as fallback_error:
                    logger.error(f"Fallback model also failed: {fallback_error}")
                    raise e  # Raise original error
            else:
                raise
        
        # Extract structured response (title, description, script)
        content = response.choices[0].message.content or ""
        structured_response = self._extract_structured_response(content)
        
        # Calculate metadata with context-aware duration estimation
        script_text = structured_response["script"]
        word_count = self._count_words(script_text)
        estimated_duration = self._estimate_duration(
            word_count=word_count,
            tts_speed=params.get('tts_speed', 1.0),
            script_type=script_type
        )
        
        return {
            "title": structured_response["title"],
            "description": structured_response["description"],
            "script": script_text,
            "word_count": word_count,
            "estimated_duration": estimated_duration,
            "provider_used": actual_provider,
            "model_used": model
        }


# Create a singleton instance
script_generator = AIScriptGenerator()