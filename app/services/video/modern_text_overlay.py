"""
Modern Text Overlay Service with advanced features, animations, and smart positioning.
CPU-optimized for high performance without GPU requirements.
"""
import os
import re
import uuid
import json
import hashlib
import logging
import tempfile
import subprocess
import unicodedata
from typing import Dict, Any, List, Tuple, Optional
from urllib.parse import urlparse
from dataclasses import dataclass, asdict

from app.utils.media import download_media_file
from app.services.s3 import s3_service
from app.services.redis_service import redis_service

logger = logging.getLogger(__name__)

@dataclass
class AnimationConfig:
    """Animation configuration for text overlays"""
    type: str = "none"  # fade_in, slide_up, typewriter, pulse, bounce, zoom_in
    duration: float = 1.0
    delay: float = 0.0
    easing: str = "ease_out"  # linear, ease_in, ease_out, ease_in_out
    repeat: int = 1

@dataclass 
class EffectsConfig:
    """Advanced effects configuration"""
    shadow_enabled: bool = False
    shadow_color: str = "black@0.5"
    shadow_offset_x: int = 2
    shadow_offset_y: int = 2
    shadow_blur: float = 0.0
    
    stroke_enabled: bool = False
    stroke_width: int = 2
    stroke_color: str = "black"
    
    glow_enabled: bool = False
    glow_color: str = "white@0.5"
    glow_size: int = 5
    
    background_enabled: bool = False
    background_color: str = "black@0.8"
    background_padding: int = 10
    background_border_radius: int = 0

@dataclass
class TypographyConfig:
    """Advanced typography configuration"""
    font_family: str = "roboto"
    font_weight: str = "regular"  # light, regular, medium, bold
    font_size: int = 48
    font_color: str = "white"
    letter_spacing: float = 0.0
    line_height: float = 1.2
    text_align: str = "center"  # left, center, right
    text_transform: str = "none"  # none, uppercase, lowercase, capitalize

@dataclass
class PositionConfig:
    """Smart positioning configuration"""
    mode: str = "preset"  # preset, custom, smart, content_aware
    preset: str = "bottom-center"
    x: str = "(w-text_w)/2"
    y: str = "h*0.85-text_h"
    margin_x: int = 20
    margin_y: int = 20
    safe_area: bool = True

@dataclass
class TextOverlayConfig:
    """Complete text overlay configuration"""
    text: str
    duration: float = 5.0
    start_time: float = 0.0
    typography: TypographyConfig = None
    effects: EffectsConfig = None
    position: PositionConfig = None
    animation: AnimationConfig = None
    auto_wrap: bool = True
    max_width_chars: int = 25

    def __post_init__(self):
        if self.typography is None:
            self.typography = TypographyConfig()
        if self.effects is None:
            self.effects = EffectsConfig()
        if self.position is None:
            self.position = PositionConfig()
        if self.animation is None:
            self.animation = AnimationConfig()


class ModernTextOverlayService:
    """Advanced text overlay service with modern video editing capabilities"""
    
    def __init__(self):
        self.font_cache = {}
        self.preset_cache = {}
        self._load_modern_presets()
        self._setup_font_paths()
        logger.info("Modern text overlay service initialized")
    
    def _setup_font_paths(self):
        """Setup font paths and variants"""
        base_font_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'fonts')
        
        self.font_variants = {
            'roboto': {
                'light': [
                    os.path.join(base_font_dir, "Roboto-Light.ttf"),
                    "/System/Library/Fonts/Helvetica.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
                ],
                'regular': [
                    os.path.join(base_font_dir, "Roboto-Regular.ttf"),
                    "/System/Library/Fonts/Helvetica.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
                ],
                'medium': [
                    os.path.join(base_font_dir, "Roboto-Medium.ttf"),
                    os.path.join(base_font_dir, "Roboto-Bold.ttf"),
                    "/System/Library/Fonts/Helvetica-Bold.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
                ],
                'bold': [
                    os.path.join(base_font_dir, "Roboto-Bold.ttf"),
                    "/System/Library/Fonts/Helvetica-Bold.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
                ]
            },
            'montserrat': {
                'light': [
                    os.path.join(base_font_dir, "Montserrat-Light.ttf"),
                    os.path.join(base_font_dir, "Roboto-Light.ttf"),
                    "/System/Library/Fonts/Helvetica.ttc"
                ],
                'regular': [
                    os.path.join(base_font_dir, "Montserrat-Regular.ttf"),
                    os.path.join(base_font_dir, "Roboto-Regular.ttf"),
                    "/System/Library/Fonts/Helvetica.ttc"
                ],
                'medium': [
                    os.path.join(base_font_dir, "Montserrat-Medium.ttf"),
                    os.path.join(base_font_dir, "Montserrat-SemiBold.ttf"),
                    os.path.join(base_font_dir, "Roboto-Medium.ttf")
                ],
                'bold': [
                    os.path.join(base_font_dir, "Montserrat-Bold.ttf"),
                    os.path.join(base_font_dir, "Roboto-Bold.ttf"),
                    "/System/Library/Fonts/Helvetica-Bold.ttc"
                ]
            },
            'inter': {
                'light': [
                    os.path.join(base_font_dir, "Inter-Light.ttf"),
                    os.path.join(base_font_dir, "Roboto-Light.ttf")
                ],
                'regular': [
                    os.path.join(base_font_dir, "Inter-Regular.ttf"),
                    os.path.join(base_font_dir, "Roboto-Regular.ttf")
                ],
                'medium': [
                    os.path.join(base_font_dir, "Inter-Medium.ttf"),
                    os.path.join(base_font_dir, "Inter-SemiBold.ttf"),
                    os.path.join(base_font_dir, "Roboto-Medium.ttf")
                ],
                'bold': [
                    os.path.join(base_font_dir, "Inter-Bold.ttf"),
                    os.path.join(base_font_dir, "Roboto-Bold.ttf")
                ]
            }
        }
    
    def get_font_path(self, family: str, weight: str) -> str:
        """Get the best available font path for the specified family and weight"""
        cache_key = f"{family}_{weight}"
        if cache_key in self.font_cache:
            return self.font_cache[cache_key]
        
        # Get font candidates
        candidates = self.font_variants.get(family, {}).get(weight, [])
        if not candidates:
            # Fallback to roboto regular
            candidates = self.font_variants['roboto']['regular']
        
        # Find first existing font
        for font_path in candidates:
            if os.path.exists(font_path):
                self.font_cache[cache_key] = font_path
                logger.info(f"Using font: {font_path} for {family} {weight}")
                return font_path
        
        # Ultimate fallback
        fallback = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        self.font_cache[cache_key] = fallback
        logger.warning(f"Using fallback font: {fallback} for {family} {weight}")
        return fallback
    
    def process_text_content(self, text: str) -> str:
        """Process text with modern Unicode support"""
        # Normalize Unicode text
        normalized = unicodedata.normalize('NFC', text)
        
        # Smart FFmpeg escaping that preserves international characters
        escape_map = {
            ':': r'\:',
            "'": r"\'",
            '"': r'\"', 
            '\\': r'\\\\',
            '[': r'\[',
            ']': r'\]',
            '%': r'\%'
        }
        
        escaped = normalized
        for char, escape in escape_map.items():
            escaped = escaped.replace(char, escape)
            
        return escaped
    
    def intelligent_text_wrapping(self, text: str, max_width_chars: int = 25, font_size: int = 48) -> str:
        """Smart text wrapping with visual width consideration"""
        # Character width estimation based on font size
        avg_char_width = font_size * 0.6
        max_pixel_width = max_width_chars * avg_char_width
        
        words = text.split()
        lines = []
        current_line = []
        current_width = 0
        
        for word in words:
            # Estimate visual width of word (considering common wide chars)
            word_width = 0
            for char in word:
                if char in 'mwMW@':
                    word_width += avg_char_width * 1.3
                elif char in 'il1':
                    word_width += avg_char_width * 0.4
                else:
                    word_width += avg_char_width
            
            space_width = avg_char_width * 0.3 if current_line else 0
            
            if current_width + word_width + space_width > max_pixel_width and current_line:
                lines.append(' '.join(current_line))
                current_line = [word]
                current_width = word_width
            else:
                current_line.append(word)
                current_width += word_width + space_width
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return '\\n'.join(lines)
    
    def get_smart_position(self, video_path: str, text_config: TextOverlayConfig) -> Tuple[str, str]:
        """Analyze video for optimal text placement"""
        try:
            # Get video dimensions
            probe_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-show_entries", "stream=width,height", "-select_streams", "v:0",
                "-of", "csv=p=0", video_path
            ]
            
            result = subprocess.run(probe_cmd, capture_output=True, text=True)
            lines = result.stdout.strip().split('\n')
            
            if len(lines) >= 2:
                width, height = map(int, lines[0].split(','))
                duration = float(lines[1])
            else:
                # Fallback dimensions
                width, height, duration = 1920, 1080, 10.0
            
            # Analyze text characteristics
            text_length = len(text_config.text)
            line_count = text_config.text.count('\\n') + 1
            
            # Smart positioning logic
            if text_config.position.mode == "content_aware":
                # For short text, use bottom center with safe margins
                if text_length < 50 and line_count <= 2:
                    x = "(w-text_w)/2"
                    y = f"h-text_h-{max(height * 0.1, 50)}"
                
                # For medium text, use top area to avoid UI elements
                elif text_length < 150:
                    x = "(w-text_w)/2" 
                    y = f"{max(height * 0.1, 50)}"
                
                # For long text, use left alignment with proper margins
                else:
                    x = f"{max(width * 0.05, 30)}"
                    y = f"h*0.75-text_h"
                    
            elif text_config.position.mode == "smart":
                # Aspect ratio aware positioning
                aspect_ratio = width / height
                
                if aspect_ratio > 1.5:  # Landscape
                    x = "(w-text_w)/2"
                    y = "h*0.85-text_h"
                elif aspect_ratio < 0.8:  # Portrait (mobile)
                    x = "(w-text_w)/2"
                    y = "h*0.75-text_h"
                else:  # Square-ish
                    x = "(w-text_w)/2"
                    y = "h*0.8-text_h"
                    
            else:  # Use custom or preset position
                x = text_config.position.x
                y = text_config.position.y
            
            return x, y
            
        except Exception as e:
            logger.warning(f"Smart positioning failed, using fallback: {e}")
            return "(w-text_w)/2", "h*0.85-text_h"
    
    def create_animation_expressions(self, animation: AnimationConfig, total_duration: float, start_time: float = 0) -> Dict[str, str]:
        """Create FFmpeg expressions for text animations"""
        end_time = start_time + total_duration
        anim_duration = animation.duration
        
        expressions = {}
        
        if animation.type == "fade_in":
            expressions['alpha'] = f"if(lt(t-{start_time},{anim_duration}),(t-{start_time})/{anim_duration},1)"
            
        elif animation.type == "fade_out":
            fade_start = end_time - anim_duration
            expressions['alpha'] = f"if(gt(t,{fade_start}),1-(t-{fade_start})/{anim_duration},1)"
            
        elif animation.type == "slide_up":
            expressions['y'] = f"y+50*max(0,1-(t-{start_time})/{anim_duration})"
            expressions['alpha'] = f"min(1,(t-{start_time})/{anim_duration*0.3})"
            
        elif animation.type == "slide_down":
            expressions['y'] = f"y-50*max(0,1-(t-{start_time})/{anim_duration})"
            expressions['alpha'] = f"min(1,(t-{start_time})/{anim_duration*0.3})"
            
        elif animation.type == "slide_left":
            expressions['x'] = f"x+100*max(0,1-(t-{start_time})/{anim_duration})"
            expressions['alpha'] = f"min(1,(t-{start_time})/{anim_duration*0.3})"
            
        elif animation.type == "slide_right":
            expressions['x'] = f"x-100*max(0,1-(t-{start_time})/{anim_duration})"
            expressions['alpha'] = f"min(1,(t-{start_time})/{anim_duration*0.3})"
            
        elif animation.type == "zoom_in":
            expressions['fontsize'] = f"fontsize*(0.1+0.9*min(1,(t-{start_time})/{anim_duration}))"
            expressions['alpha'] = f"min(1,(t-{start_time})/{anim_duration*0.5})"
            
        elif animation.type == "pulse":
            expressions['fontsize'] = f"fontsize*(1+0.1*sin(2*PI*(t-{start_time})*2))"
            
        elif animation.type == "bounce":
            expressions['y'] = f"y-abs(sin(PI*(t-{start_time})/{anim_duration}))*20"
            
        elif animation.type == "typewriter":
            text_len = len(animation.text) if hasattr(animation, 'text') else 50
            expressions['text'] = f"substr(text,0,max(0,min({text_len},floor((t-{start_time})/{anim_duration}*{text_len}))))"
        
        return expressions
    
    def build_advanced_filter_chain(self, config: TextOverlayConfig, video_path: str) -> str:
        """Build comprehensive FFmpeg filter chain with all modern features"""
        filters = []
        
        # Get font path
        font_path = self.get_font_path(config.typography.font_family, config.typography.font_weight)
        
        # Process text content
        if config.auto_wrap:
            processed_text = self.intelligent_text_wrapping(
                config.text, config.max_width_chars, config.typography.font_size
            )
        else:
            processed_text = config.text
            
        escaped_text = self.process_text_content(processed_text)
        
        # Get smart positioning
        x_pos, y_pos = self.get_smart_position(video_path, config)
        
        # Base text configuration
        base_params = {
            'fontfile': font_path,
            'text': f"'{escaped_text}'",
            'fontsize': config.typography.font_size,
            'fontcolor': config.typography.font_color,
            'x': x_pos,
            'y': y_pos
        }
        
        # Add letter spacing if specified
        if config.typography.letter_spacing != 0:
            base_params['letter_spacing'] = config.typography.letter_spacing
        
        # Add line spacing
        line_spacing = int(config.typography.font_size * (config.typography.line_height - 1))
        base_params['line_spacing'] = line_spacing
        
        # Add effects
        if config.effects.stroke_enabled:
            base_params['borderw'] = config.effects.stroke_width
            base_params['bordercolor'] = config.effects.stroke_color
            
        if config.effects.shadow_enabled:
            base_params['shadowcolor'] = config.effects.shadow_color
            base_params['shadowx'] = config.effects.shadow_offset_x
            base_params['shadowy'] = config.effects.shadow_offset_y
            
        if config.effects.background_enabled:
            base_params['box'] = 1
            base_params['boxcolor'] = config.effects.background_color
            base_params['boxborderw'] = config.effects.background_padding
        
        # Add animation expressions
        if config.animation.type != "none":
            animation_exprs = self.create_animation_expressions(
                config.animation, config.duration, config.start_time
            )
            base_params.update(animation_exprs)
        
        # Add timing
        enable_expr = f"between(t,{config.start_time},{config.start_time + config.duration})"
        base_params['enable'] = enable_expr
        
        # Build the drawtext filter
        params_str = ':'.join([f"{k}={v}" for k, v in base_params.items()])
        drawtext_filter = f"drawtext={params_str}"
        
        # Add glow effect as separate filter if enabled
        if config.effects.glow_enabled:
            # Create glow by duplicating text with blur
            glow_params = base_params.copy()
            glow_params['fontcolor'] = config.effects.glow_color
            glow_params['fontsize'] = config.typography.font_size + config.effects.glow_size
            
            glow_params_str = ':'.join([f"{k}={v}" for k, v in glow_params.items()])
            glow_filter = f"drawtext={glow_params_str}"
            filters.append(glow_filter)
        
        filters.append(drawtext_filter)
        return ','.join(filters)
    
    def _load_modern_presets(self):
        """Load modern text overlay presets"""
        self.modern_presets = {
            "tiktok_viral": {
                "name": "TikTok Viral",
                "description": "High-engagement TikTok-style captions with bold styling",
                "category": "social_media",
                "config": TextOverlayConfig(
                    text="",
                    duration=4.0,
                    typography=TypographyConfig(
                        font_family="montserrat",
                        font_weight="bold",
                        font_size=52,
                        font_color="white",
                        text_align="center"
                    ),
                    effects=EffectsConfig(
                        stroke_enabled=True,
                        stroke_width=3,
                        stroke_color="black",
                        shadow_enabled=True,
                        shadow_color="black@0.7",
                        shadow_offset_x=2,
                        shadow_offset_y=2
                    ),
                    position=PositionConfig(
                        mode="smart",
                        preset="bottom-center"
                    ),
                    animation=AnimationConfig(
                        type="fade_in",
                        duration=0.8
                    )
                )
            },
            
            "instagram_story": {
                "name": "Instagram Story",
                "description": "Clean Instagram Story text with modern aesthetics",
                "category": "social_media", 
                "config": TextOverlayConfig(
                    text="",
                    duration=6.0,
                    typography=TypographyConfig(
                        font_family="inter",
                        font_weight="medium", 
                        font_size=44,
                        font_color="white",
                        text_align="center"
                    ),
                    effects=EffectsConfig(
                        background_enabled=True,
                        background_color="black@0.6",
                        background_padding=20,
                        background_border_radius=12
                    ),
                    position=PositionConfig(
                        mode="preset",
                        preset="top-center",
                        y="h*0.15"
                    ),
                    animation=AnimationConfig(
                        type="slide_up",
                        duration=1.0
                    )
                )
            },
            
            "youtube_title": {
                "name": "YouTube Title",
                "description": "Eye-catching YouTube thumbnail-style title",
                "category": "video_platform",
                "config": TextOverlayConfig(
                    text="",
                    duration=8.0,
                    typography=TypographyConfig(
                        font_family="roboto",
                        font_weight="bold",
                        font_size=64,
                        font_color="yellow",
                        text_align="center"
                    ),
                    effects=EffectsConfig(
                        stroke_enabled=True,
                        stroke_width=4,
                        stroke_color="red",
                        shadow_enabled=True,
                        shadow_color="black@0.9",
                        shadow_offset_x=3,
                        shadow_offset_y=3
                    ),
                    position=PositionConfig(
                        mode="preset",
                        preset="center",
                        y="h*0.3"
                    ),
                    animation=AnimationConfig(
                        type="zoom_in",
                        duration=1.2
                    )
                )
            },
            
            "minimalist_quote": {
                "name": "Minimalist Quote",
                "description": "Clean, elegant quote presentation",
                "category": "professional",
                "config": TextOverlayConfig(
                    text="",
                    duration=10.0,
                    typography=TypographyConfig(
                        font_family="inter",
                        font_weight="light",
                        font_size=36,
                        font_color="white",
                        text_align="center",
                        line_height=1.4
                    ),
                    effects=EffectsConfig(
                        shadow_enabled=True,
                        shadow_color="black@0.3",
                        shadow_offset_x=1,
                        shadow_offset_y=1
                    ),
                    position=PositionConfig(
                        mode="preset",
                        preset="center"
                    ),
                    animation=AnimationConfig(
                        type="fade_in",
                        duration=2.0
                    )
                )
            },
            
            "neon_glow": {
                "name": "Neon Glow",
                "description": "Futuristic neon-style text with glow effects",
                "category": "creative",
                "config": TextOverlayConfig(
                    text="",
                    duration=6.0,
                    typography=TypographyConfig(
                        font_family="montserrat",
                        font_weight="bold",
                        font_size=56,
                        font_color="cyan",
                        text_align="center"
                    ),
                    effects=EffectsConfig(
                        glow_enabled=True,
                        glow_color="cyan@0.6",
                        glow_size=8,
                        stroke_enabled=True,
                        stroke_width=2,
                        stroke_color="white"
                    ),
                    position=PositionConfig(
                        mode="preset",
                        preset="center"
                    ),
                    animation=AnimationConfig(
                        type="pulse",
                        duration=2.0
                    )
                )
            },
            
            "news_alert": {
                "name": "News Alert",
                "description": "Breaking news style with urgency",
                "category": "broadcast",
                "config": TextOverlayConfig(
                    text="",
                    duration=5.0,
                    typography=TypographyConfig(
                        font_family="roboto",
                        font_weight="bold",
                        font_size=42,
                        font_color="white",
                        text_align="left",
                        text_transform="uppercase"
                    ),
                    effects=EffectsConfig(
                        background_enabled=True,
                        background_color="red@0.9",
                        background_padding=15
                    ),
                    position=PositionConfig(
                        mode="preset",
                        preset="bottom-left",
                        x="30",
                        y="h-text_h-50"
                    ),
                    animation=AnimationConfig(
                        type="slide_left",
                        duration=1.0
                    )
                )
            }
        }
    
    def get_modern_presets(self) -> Dict[str, Any]:
        """Get all modern text overlay presets"""
        return {
            name: {
                "name": preset["name"],
                "description": preset["description"], 
                "category": preset["category"],
                "preview_config": asdict(preset["config"])
            }
            for name, preset in self.modern_presets.items()
        }
    
    def optimize_cpu_performance(self) -> Dict[str, str]:
        """Get CPU-optimized FFmpeg settings"""
        cpu_count = os.cpu_count() or 4
        optimal_threads = min(cpu_count - 1, 6)  # Leave one core free, max 6 threads
        
        return {
            'threads': str(optimal_threads),
            'preset': 'faster',  # Good balance of speed/quality
            'crf': '23',         # Good quality setting
            'tune': 'fastdecode' # Optimize for playback
        }
    
    async def generate_preview(self, video_url: str, config: TextOverlayConfig) -> Dict[str, Any]:
        """Generate a fast preview for real-time feedback"""
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Download video
            input_path, _ = await download_media_file(video_url, temp_dir)
            
            # Create preview (3 seconds, low resolution)
            preview_filename = f"preview_{uuid.uuid4().hex[:8]}.mp4"
            preview_path = os.path.join(temp_dir, preview_filename)
            
            # Build filter chain
            filter_chain = self.build_advanced_filter_chain(config, input_path)
            
            # CPU-optimized preview command
            preview_cmd = [
                "ffmpeg", "-i", input_path,
                "-t", "3",  # 3 second preview
                "-vf", f"scale=640:360,{filter_chain}",  # Lower resolution
                "-c:v", "libx264", "-preset", "ultrafast",
                "-crf", "28",  # Lower quality for speed
                "-an",  # No audio
                "-y", preview_path
            ]
            
            result = subprocess.run(preview_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"Preview generation failed: {result.stderr}")
            
            # Upload preview to S3
            s3_key = f"previews/text_overlay/{preview_filename}"
            preview_url = await s3_service.upload_file(preview_path, s3_key)
            
            return {
                "preview_url": preview_url,
                "duration": 3.0,
                "resolution": "640x360"
            }
            
        finally:
            # Cleanup
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    async def get_cached_preview(self, video_url: str, config: TextOverlayConfig) -> Optional[Dict[str, Any]]:
        """Get cached preview if available"""
        try:
            # Create cache key from config
            config_dict = asdict(config)
            config_dict['video_url'] = video_url
            config_json = json.dumps(config_dict, sort_keys=True)
            cache_key = f"text_overlay_preview:{hashlib.md5(config_json.encode()).hexdigest()}"
            
            # Check cache
            cached_result = await redis_service.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
                
            # Generate new preview
            preview_result = await self.generate_preview(video_url, config)
            
            # Cache for 1 hour
            await redis_service.set(cache_key, json.dumps(preview_result), expire=3600)
            
            return preview_result
            
        except Exception as e:
            logger.warning(f"Preview caching failed: {e}")
            return await self.generate_preview(video_url, config)
    
    async def create_text_overlay(self, video_url: str, config: TextOverlayConfig) -> Dict[str, Any]:
        """Create text overlay with modern features"""
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Download input video
            input_path, _ = await download_media_file(video_url, temp_dir)
            
            # Generate output filename
            output_filename = f"text_overlay_{uuid.uuid4().hex[:8]}.mp4"
            output_path = os.path.join(temp_dir, output_filename)
            
            # Build advanced filter chain
            filter_chain = self.build_advanced_filter_chain(config, input_path)
            
            # Get CPU optimization settings
            cpu_settings = self.optimize_cpu_performance()
            
            # Build FFmpeg command
            ffmpeg_cmd = [
                "ffmpeg", "-i", input_path,
                "-vf", filter_chain,
                "-c:v", "libx264",
                "-preset", cpu_settings['preset'],
                "-crf", cpu_settings['crf'],
                "-threads", cpu_settings['threads'],
                "-tune", cpu_settings['tune'],
                "-c:a", "copy",  # Copy audio without re-encoding
                "-movflags", "+faststart",  # Optimize for streaming
                "-y", output_path
            ]
            
            # Execute with proper environment
            env = os.environ.copy()
            env.update({
                'LC_ALL': 'C.UTF-8',
                'LANG': 'C.UTF-8',
                'PYTHONIOENCODING': 'utf-8'
            })
            
            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, env=env)
            if result.returncode != 0:
                raise Exception(f"Text overlay processing failed: {result.stderr}")
            
            # Upload to S3
            s3_key = f"text_overlays/{output_filename}"
            video_output_url = await s3_service.upload_file(output_path, s3_key)
            
            # Get video info
            probe_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", output_path
            ]
            duration_result = subprocess.run(probe_cmd, capture_output=True, text=True)
            duration = float(duration_result.stdout.strip()) if duration_result.stdout.strip() else 0.0
            
            logger.info(f"Text overlay created successfully: {video_output_url}")
            
            return {
                "video_url": video_output_url,
                "duration": duration,
                "config_used": asdict(config),
                "processing_time": "optimized"
            }
            
        finally:
            # Cleanup
            import shutil  
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    async def process_modern_text_overlay_job(self, job_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process text overlay job with modern features"""
        try:
            video_url = data.get('video_url')
            if not video_url:
                raise ValueError("video_url is required")
            
            # Parse configuration
            if 'config' in data:
                # Modern config format
                config_data = data['config']
                config = TextOverlayConfig(
                    text=config_data.get('text', ''),
                    duration=config_data.get('duration', 5.0),
                    start_time=config_data.get('start_time', 0.0),
                    typography=TypographyConfig(**config_data.get('typography', {})),
                    effects=EffectsConfig(**config_data.get('effects', {})),
                    position=PositionConfig(**config_data.get('position', {})),
                    animation=AnimationConfig(**config_data.get('animation', {})),
                    auto_wrap=config_data.get('auto_wrap', True),
                    max_width_chars=config_data.get('max_width_chars', 25)
                )
            else:
                # Legacy format support
                text = data.get('text', '')
                options = data.get('options', {})
                preset_name = data.get('preset_used')
                
                if preset_name and preset_name in self.modern_presets:
                    # Use modern preset
                    config = self.modern_presets[preset_name]["config"]
                    config.text = text
                    # Override with any provided options
                    if 'duration' in options:
                        config.duration = options['duration']
                    if 'font_size' in options:
                        config.typography.font_size = options['font_size']
                    if 'font_color' in options:
                        config.typography.font_color = options['font_color']
                else:
                    # Convert legacy options to modern config
                    config = TextOverlayConfig(
                        text=text,
                        duration=options.get('duration', 5.0),
                        typography=TypographyConfig(
                            font_size=options.get('font_size', 48),
                            font_color=options.get('font_color', 'white')
                        ),
                        effects=EffectsConfig(
                            background_enabled=bool(options.get('box_color')),
                            background_color=f"{options.get('box_color', 'black')}@{options.get('box_opacity', 0.8)}",
                            background_padding=options.get('boxborderw', 60)
                        ),
                        position=PositionConfig(
                            preset=options.get('position', 'bottom-center'),
                            y=f"h-text_h-{options.get('y_offset', 50)}"
                        ),
                        auto_wrap=options.get('auto_wrap', True)
                    )
            
            logger.info(f"Processing modern text overlay job {job_id}")
            result = await self.create_text_overlay(video_url, config)
            
            return result
            
        except Exception as e:
            logger.error(f"Modern text overlay job {job_id} failed: {str(e)}", exc_info=True)
            raise Exception(f"Text overlay processing failed: {str(e)}")


# Create singleton instance
modern_text_overlay_service = ModernTextOverlayService()