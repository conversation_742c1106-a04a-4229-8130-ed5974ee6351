"""
Database configuration and models for the Ouinhi web application.
"""
import os
from datetime import datetime, timezone
from typing import Op<PERSON>, AsyncGenerator
from sqlalchemy import String, Text, DateTime, Enum as SQLE<PERSON>, <PERSON>SON, Integer, <PERSON><PERSON><PERSON>, Float, <PERSON>Key
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from app.models import JobStatus
import enum
from loguru import logger

def utcnow():
    """Get current UTC time as timezone-naive datetime for database storage."""
    return datetime.now(timezone.utc).replace(tzinfo=None)

class Base(DeclarativeBase):
    pass

class UserRole(enum.Enum):
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"

class APIKeyStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    REVOKED = "revoked"

class EndpointCategory(enum.Enum):
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    MEDIA = "media"
    UTILITY = "utility"

# MediaType and MediaCategory enums are defined later in the file (lines 205-237)
# to avoid conflicts with the updated definitions

class User(Base):
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    role: Mapped[UserRole] = mapped_column(SQLEnum(UserRole, values_callable=lambda obj: [e.value for e in obj]), nullable=False, default=UserRole.USER)
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Relationships
    api_keys: Mapped[list["APIKey"]] = relationship("APIKey", back_populates="user")
    projects: Mapped[list["Project"]] = relationship("Project", back_populates="owner")

class APIKey(Base):
    __tablename__ = "api_keys"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    key_id: Mapped[str] = mapped_column(String(36), unique=True, nullable=False)  # Public key identifier
    key_hash: Mapped[str] = mapped_column(String(255), nullable=False)  # Hashed actual key
    name: Mapped[str] = mapped_column(String(100), nullable=False)  # User-friendly name
    status: Mapped[APIKeyStatus] = mapped_column(SQLEnum(APIKeyStatus, values_callable=lambda obj: [e.value for e in obj]), nullable=False, default=APIKeyStatus.ACTIVE)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Usage limits
    rate_limit_per_hour: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    monthly_quota: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Tracking
    total_requests: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_used: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="api_keys")
    usage_logs: Mapped[list["APIUsage"]] = relationship("APIUsage", back_populates="api_key")

class Project(Base):
    __tablename__ = "projects"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    is_public: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow, onupdate=utcnow)
    
    # Relationships
    owner: Mapped["User"] = relationship("User", back_populates="projects")
    jobs: Mapped[list["JobRecord"]] = relationship("JobRecord", back_populates="project")

class APIEndpoint(Base):
    __tablename__ = "api_endpoints"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    path: Mapped[str] = mapped_column(String(255), nullable=False)
    method: Mapped[str] = mapped_column(String(10), nullable=False)  # GET, POST, etc.
    category: Mapped[EndpointCategory] = mapped_column(SQLEnum(EndpointCategory, values_callable=lambda obj: [e.value for e in obj]), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    requires_auth: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    
    # Usage statistics
    total_calls: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    avg_response_time: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    success_rate: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow, onupdate=utcnow)
    
    # Relationships
    usage_logs: Mapped[list["APIUsage"]] = relationship("APIUsage", back_populates="endpoint")

class APIUsage(Base):
    __tablename__ = "api_usage"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    api_key_id: Mapped[int] = mapped_column(ForeignKey("api_keys.id"), nullable=False)
    endpoint_id: Mapped[int] = mapped_column(ForeignKey("api_endpoints.id"), nullable=False)
    
    # Request details
    request_method: Mapped[str] = mapped_column(String(10), nullable=False)
    request_path: Mapped[str] = mapped_column(String(255), nullable=False)
    response_status: Mapped[int] = mapped_column(Integer, nullable=False)
    response_time_ms: Mapped[float] = mapped_column(Float, nullable=False)
    
    # Tracking
    user_agent: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)  # IPv6 support
    timestamp: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    
    # Job reference if applicable
    job_id: Mapped[Optional[str]] = mapped_column(String(36), nullable=True)
    
    # Relationships
    api_key: Mapped["APIKey"] = relationship("APIKey", back_populates="usage_logs")
    endpoint: Mapped["APIEndpoint"] = relationship("APIEndpoint", back_populates="usage_logs")

class JobRecord(Base):
    __tablename__ = "jobs"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True)
    operation: Mapped[str] = mapped_column(String(100), nullable=False)
    params: Mapped[dict] = mapped_column(JSON, nullable=False)
    status: Mapped[JobStatus] = mapped_column(SQLEnum(JobStatus, name="jobstatus", native_enum=True, create_constraint=False, values_callable=lambda obj: [e.value for e in obj]), nullable=False, default=JobStatus.PENDING)
    result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Enhanced fields for web app
    project_id: Mapped[Optional[int]] = mapped_column(ForeignKey("projects.id"), nullable=True)
    api_key_id: Mapped[Optional[int]] = mapped_column(ForeignKey("api_keys.id"), nullable=True)
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=0)  # 0=normal, 1=high, -1=low
    
    # Progress tracking
    progress_percentage: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    estimated_completion: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Resource usage
    processing_time_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    memory_used_mb: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow, onupdate=utcnow)
    
    # Relationships
    project: Mapped[Optional["Project"]] = relationship("Project", back_populates="jobs")

class UserSession(Base):
    __tablename__ = "user_sessions"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    last_activity: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    expires_at: Mapped[datetime] = mapped_column(DateTime, nullable=False)

class VideoType(enum.Enum):
    FOOTAGE_TO_VIDEO = "footage_to_video"
    AIIMAGE_TO_VIDEO = "aiimage_to_video" 
    SCENES_TO_VIDEO = "scenes_to_video"
    SHORT_VIDEO_CREATION = "short_video_creation"
    IMAGE_TO_VIDEO = "image_to_video"
    OTHER = "other"

class MediaType(enum.Enum):
    VIDEO = "video"
    AUDIO = "audio"
    IMAGE = "image"
    DOCUMENT = "document"
    OTHER = "other"

class MediaCategory(enum.Enum):
    # Video categories
    FOOTAGE_TO_VIDEO = "footage_to_video"
    AIIMAGE_TO_VIDEO = "aiimage_to_video"
    SCENES_TO_VIDEO = "scenes_to_video"
    SHORT_VIDEO_CREATION = "short_video_creation"
    IMAGE_TO_VIDEO = "image_to_video"
    
    # Audio categories
    TEXT_TO_SPEECH = "text_to_speech"
    MUSIC_GENERATION = "music_generation"
    AUDIO_TRANSCRIPTION = "audio_transcription"
    VOICE_CLONING = "voice_cloning"
    
    # Image categories
    IMAGE_GENERATION = "image_generation"
    IMAGE_EDITING = "image_editing"
    IMAGE_UPSCALING = "image_upscaling"
    
    # Media processing categories
    MEDIA_DOWNLOAD = "media_download"
    MEDIA_CONVERSION = "media_conversion"
    METADATA_EXTRACTION = "metadata_extraction"
    YOUTUBE_TRANSCRIPT = "youtube_transcript"
    
    OTHER = "other"

class VideoRecord(Base):
    __tablename__ = "videos"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True)  # Same as job_id
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    video_type: Mapped[VideoType] = mapped_column(SQLEnum(VideoType, values_callable=lambda obj: [e.value for e in obj]), nullable=False)
    
    # Video file URLs (S3 storage)
    final_video_url: Mapped[str] = mapped_column(String(500), nullable=False)
    video_with_audio_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    audio_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    srt_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    thumbnail_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Video metadata
    duration_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    resolution: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # e.g., "1080x1920"
    file_size_mb: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    word_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    segments_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Generation metadata
    script_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    voice_provider: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    voice_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    language: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    
    # Processing metadata
    processing_time_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    background_videos_used: Mapped[Optional[list]] = mapped_column(JSON, nullable=True)
    generation_params: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Organization
    project_id: Mapped[Optional[int]] = mapped_column(ForeignKey("projects.id"), nullable=True)
    api_key_id: Mapped[Optional[int]] = mapped_column(ForeignKey("api_keys.id"), nullable=True)
    tags: Mapped[Optional[list]] = mapped_column(JSON, nullable=True)  # User-defined tags
    
    # Status and tracking
    is_deleted: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    download_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_accessed: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow, onupdate=utcnow)
    
    # Relationships
    project: Mapped[Optional["Project"]] = relationship("Project", back_populates=None)
    api_key: Mapped[Optional["APIKey"]] = relationship("APIKey", back_populates=None)

class MediaRecord(Base):
    """Unified media library for all generated content (videos, audio, images, documents)."""
    __tablename__ = "media_library"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True)  # Same as job_id
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    media_type: Mapped[MediaType] = mapped_column(SQLEnum(MediaType, name="mediatype", native_enum=True, create_constraint=False, values_callable=lambda obj: [e.value for e in obj]), nullable=False)
    category: Mapped[MediaCategory] = mapped_column(SQLEnum(MediaCategory, name="mediacategory", native_enum=True, create_constraint=False, values_callable=lambda obj: [e.value for e in obj]), nullable=False)
    
    # Primary file URL (main output)
    primary_url: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # Additional file URLs (related outputs)
    secondary_urls: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)  # {"audio": "url", "srt": "url", etc.}
    thumbnail_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Media metadata
    duration_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # For video/audio
    dimensions: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)  # {"width": 1080, "height": 1920}
    file_size_mb: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    format: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # mp4, mp3, png, etc.
    
    # Content metadata
    word_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    text_content: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # Script, transcript, etc.
    
    # Generation metadata
    prompt: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # Original prompt/input
    model_used: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    provider: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    language: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    
    # Processing metadata
    processing_time_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    generation_params: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Organization
    project_id: Mapped[Optional[int]] = mapped_column(ForeignKey("projects.id"), nullable=True)
    api_key_id: Mapped[Optional[int]] = mapped_column(ForeignKey("api_keys.id"), nullable=True)
    tags: Mapped[Optional[list]] = mapped_column(JSON, nullable=True)  # User-defined tags
    
    # Status and tracking
    is_deleted: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    is_favorite: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    download_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    view_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_accessed: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=utcnow, onupdate=utcnow)
    
    # Relationships
    project: Mapped[Optional["Project"]] = relationship("Project", back_populates=None)
    api_key: Mapped[Optional["APIKey"]] = relationship("APIKey", back_populates=None)

class DatabaseService:
    """Database service for job persistence."""
    
    def __init__(self):
        self.engine = None
        self.async_session = None
        
    async def initialize(self):
        """Initialize database connection."""
        database_url = os.getenv(
            "DATABASE_URL", 
            "postgresql+asyncpg://postgres:postgres@postgres:5432/ouinhi"
        )
        
        self.engine = create_async_engine(
            database_url,
            echo=False,  # Set to True for SQL debugging
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        self.async_session = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        logger.info("Database service initialized")
        
    async def create_tables(self):
        """Create database tables."""
        if not self.engine:
            await self.initialize()
        
        if self.engine:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created")
        else:
            logger.error("Failed to initialize database engine")
    
    async def update_enums(self):
        """Update database enums to include any missing values."""
        if not self.engine:
            await self.initialize()
        
        if self.engine:
            try:
                from sqlalchemy import text
                async with self.engine.begin() as conn:
                    # Check and add missing enum values for mediacategory
                    result = await conn.execute(text("""
                        SELECT enumlabel FROM pg_enum 
                        WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'mediacategory')
                    """))
                    media_categories = [row[0] for row in result]
                    
                    if 'footage_to_video' not in media_categories:
                        await conn.execute(text("ALTER TYPE mediacategory ADD VALUE 'footage_to_video'"))
                        logger.info("Added 'footage_to_video' to mediacategory enum")
                    
                    # Check and add missing enum values for videotype
                    result = await conn.execute(text("""
                        SELECT enumlabel FROM pg_enum 
                        WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'videotype')
                    """))
                    video_types = [row[0] for row in result]
                    
                    if 'footage_to_video' not in video_types:
                        await conn.execute(text("ALTER TYPE videotype ADD VALUE 'footage_to_video'"))
                        logger.info("Added 'footage_to_video' to videotype enum")
                        
                logger.info("Database enum update completed")
            except Exception as e:
                logger.warning(f"Database enum update failed (this is OK if enums don't exist yet): {e}")
        else:
            logger.error("Failed to initialize database engine for enum update")
        
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session."""
        if not self.async_session:
            await self.initialize()
        
        if self.async_session:
            async with self.async_session() as session:
                try:
                    yield session
                except Exception:
                    await session.rollback()
                    raise
                finally:
                    await session.close()
        else:
            raise RuntimeError("Failed to initialize database session")
                
    async def close(self):
        """Close database connection."""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database connection closed")

# Global database service instance
database_service = DatabaseService()