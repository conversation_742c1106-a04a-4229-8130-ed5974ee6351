"""
FastAPI application for media generation.
"""
from fastapi import <PERSON>AP<PERSON>, Depends, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
import os
import asyncio
import logging
from dotenv import load_dotenv
from fastapi.openapi.models import SecurityScheme
from fastapi.openapi.utils import get_openapi
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

# Load environment variables from .env file if it exists
load_dotenv()

# Initialize enhanced logging and hardware optimization
from app.utils.logging import configure_enhanced_logging, get_logger
from app.utils.hardware import apply_hardware_optimizations, get_device_info

# Configure enhanced logging system
configure_enhanced_logging()

# Apply hardware optimizations
apply_hardware_optimizations()

# Get enhanced logger
logger = get_logger(module="main", component="fastapi_app")

# Load environment variables and log them with rich context
device_info = get_device_info()
logger.bind(
    aws_region=os.environ.get('S3_REGION', 'Not set'),
    aws_bucket=os.environ.get('S3_BUCKET_NAME', 'Not set'),
    log_level=os.environ.get('LOG_LEVEL', 'Not set'),
    debug_mode=os.environ.get('DEBUG', 'Not set'),
    **device_info
).info("Ouinhi API initialization")

# Import authentication and middleware
from app.utils.auth import get_api_key
from app.middleware import SecurityMiddleware

# Import routers
from app.routes.auth import router as auth_router
from app.routes.dashboard import router as dashboard_router
from app.routes.image.image_to_video import router as video_generations_router
from app.routes.image.image_overlay import router as image_edit_router
from app.routes.image.video_overlay import router as video_edit_router
from app.routes.image.generate import router as image_generate_router
from app.routes.image.enhancement import router as image_enhancement_router
from app.routes.audio import router as audio_router
from app.routes.media import router as media_router
from app.routes.video import router as video_router

from app.routes.admin import router as admin_router
from app.routes.ffmpeg import router as ffmpeg_router
from app.routes.s3 import router as s3_router
from app.routes.code import router as code_router
from app.routes.media_conversions import router as conversions_router
from app.routes.ai.script_generation import router as ai_script_router
from app.routes.ai.video_search import router as ai_video_search_router
from app.routes.ai.image_search import router as ai_image_search_router
from app.routes.ai.footage_to_video import router as ai_footage_to_video_router
from app.routes.ai.aiimage_to_video import router as ai_aiimage_to_video_router
from app.routes.ai.scenes_to_video import router as ai_scenes_to_video_router
from app.routes.ai.general import router as ai_general_router
from app.routes.simone import router as simone_router
from app.routes.yt_shorts.shorts_routes import router as yt_shorts_router
from app.routes.documents import router as documents_router
from app.routes.mcp.video_creator import router as mcp_router
from app.routes.openai_compat import router as openai_compat_router
from app.routes.jobs import router as jobs_router
from app.routes.diagnostics import router as diagnostics_router
from app.routes.music import router as music_router
from app.routes.postiz import router as postiz_router
from app.routes.videos import router as videos_router
from app.routes.admin_jobs import router as admin_jobs_router
from app.routes.admin_users import router as admin_users_router
from app.routes.library import router as library_router

# Pollinations.AI routers
from app.routes.image.pollinations import router as pollinations_image_router
from app.routes.ai.pollinations_text import router as pollinations_text_router
from app.routes.audio.pollinations import router as pollinations_audio_router

# Create rate limiter
limiter = Limiter(key_func=get_remote_address)

# Create application
app = FastAPI(
    title="Ouinhi API",
    description="API for generating media content without coding",
    version="0.1.0"
)

# Add rate limiter to app state
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)  # type: ignore

# Custom exception handler for request validation errors to prevent binary data encoding issues
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors without exposing binary data"""
    try:
        # Log the full error for debugging
        logger.error(f"Validation error on {request.method} {request.url.path}: {exc.errors()}")
        
        # Extract only safe error information
        safe_errors = []
        for error in exc.errors():
            safe_error = {
                "type": error.get("type", "validation_error"),
                "loc": error.get("loc", []),
                "msg": error.get("msg", "Validation error")
            }
            # Don't include input data which might contain binary content
            safe_errors.append(safe_error)
        
        return JSONResponse(
            status_code=422,
            content={"detail": safe_errors}
        )
    except Exception as e:
        # Fallback error response if anything goes wrong
        logger.error(f"Error in validation exception handler: {e}")
        return JSONResponse(
            status_code=422,
            content={"detail": "Request validation failed"}
        )

# Add security middleware
app.add_middleware(SecurityMiddleware)

# Add API key security scheme to OpenAPI documentation
app.openapi_schema = None  # Reset schema to rebuild it
app.swagger_ui_init_oauth = None
app.openapi_tags = [
    {"name": "auth", "description": "Authentication endpoints"},
    {"name": "dashboard", "description": "Dashboard management and statistics"},
    {"name": "admin", "description": "Admin dashboard and management"},
    {"name": "image", "description": "Image processing endpoints"},
    {"name": "Image Generation", "description": "AI-powered image generation from text prompts"},
    {"name": "audio", "description": "Audio processing endpoints"},
    {"name": "media", "description": "Media processing endpoints"},
    {"name": "videos", "description": "Video processing endpoints"},
    {"name": "ffmpeg", "description": "FFmpeg compose operations"},
    {"name": "AI Script Generation", "description": "AI-powered script generation for video content"},
    {"name": "AI Video Search", "description": "AI-powered video search and stock footage integration"},
    {"name": "AI Video Generation", "description": "End-to-end AI video generation from topics"},
    {"name": "simone", "description": "AI-powered video to blog post conversion with social media content"},
    {"name": "yt-shorts", "description": "Comprehensive YouTube Shorts generation with advanced AI features"},
    {"name": "documents", "description": "Document processing and conversion to Markdown using MarkItDown"},
    {"name": "mcp", "description": "Model Context Protocol server for AI agent integration"},
    {"name": "Jobs", "description": "Job management and status tracking"},
    {"name": "Diagnostics", "description": "Service health and API key configuration diagnostics"},
    {"name": "Logging Control", "description": "Runtime logging configuration and quiet mode management"},
    {"name": "Admin - Job Management", "description": "Job cleanup and maintenance operations"},
    {"name": "Admin - User Management", "description": "User management and administration operations"},
    {"name": "Pollinations Image", "description": "Pollinations.AI image generation and vision analysis"},
    {"name": "Pollinations Text", "description": "Pollinations.AI text generation and chat completions"},
    {"name": "Pollinations Audio", "description": "Pollinations.AI text-to-speech and audio transcription"},
    {"name": "WaveSpeed Video Generation", "description": "WaveSpeedAI image-to-video generation with advanced motion synthesis"},
]

def custom_openapi():
    if app.openapi_schema:  # Use the cached schema if it exists
        return app.openapi_schema

    # Create the base OpenAPI schema
    openapi_schema = get_openapi(
        title="Ouinhi API",
        version="1.0.0",
        description="API for media processing and transformation",
        routes=app.routes,
    )

    # Customize the schema as needed
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }

    # Cache the schema
    app.openapi_schema = openapi_schema
    return app.openapi_schema

# Replace FastAPI's default openapi() with our custom one
app.openapi = custom_openapi

# Ensure temporary files directory exists
os.makedirs("temp", exist_ok=True)
os.makedirs("temp/output", exist_ok=True)

# Include routers with authentication dependency under /api prefix
app.include_router(dashboard_router, prefix="/api/v1", dependencies=[Depends(get_api_key)])
app.include_router(video_generations_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(image_edit_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(video_edit_router, prefix="/api", dependencies=[Depends(get_api_key)])
# Include image generation router with API key dependency
app.include_router(image_generate_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(image_enhancement_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(audio_router, prefix="/api/v1/audio", dependencies=[Depends(get_api_key)])
app.include_router(media_router, prefix="/api/v1/media", dependencies=[Depends(get_api_key)])
app.include_router(video_router, prefix="/api/v1/videos", dependencies=[Depends(get_api_key)])

app.include_router(ffmpeg_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(s3_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(code_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(conversions_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(ai_script_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(ai_video_search_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(ai_image_search_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(ai_footage_to_video_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(ai_aiimage_to_video_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(ai_scenes_to_video_router, prefix="/api/v1/ai", dependencies=[Depends(get_api_key)])
app.include_router(ai_general_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(simone_router, prefix="/api/v1", dependencies=[Depends(get_api_key)])
app.include_router(yt_shorts_router, prefix="/api/v1", dependencies=[Depends(get_api_key)])
app.include_router(documents_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(mcp_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(openai_compat_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(jobs_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(diagnostics_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(music_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(postiz_router, prefix="/api/v1", dependencies=[Depends(get_api_key)])
app.include_router(videos_router, prefix="/api/v1", dependencies=[Depends(get_api_key)])
app.include_router(admin_jobs_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(admin_users_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(library_router, prefix="/api/v1/library", dependencies=[Depends(get_api_key)])

# Pollinations.AI routers 
app.include_router(pollinations_image_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(pollinations_text_router, prefix="/api", dependencies=[Depends(get_api_key)])
app.include_router(pollinations_audio_router, prefix="/api", dependencies=[Depends(get_api_key)])

# Include auth router (no authentication dependency for login)
app.include_router(auth_router)

# Include admin router (no authentication dependency for login pages, but endpoints handle auth internally)
app.include_router(admin_router)

# Serve static files for the React frontend
frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "frontend", "dist")
if os.path.exists(frontend_path):
    # Mount static assets at /assets for Vite build output
    app.mount("/assets", StaticFiles(directory=os.path.join(frontend_path, "assets")), name="assets")
    # Mount all static files at /static as well (fallback)
    app.mount("/static", StaticFiles(directory=frontend_path), name="static")
    logger.info(f"Serving React frontend from: {frontend_path}")
    
    # Serve the React app at /dashboard/* and /dashboard (SPA routing)
    @app.get("/dashboard/{path:path}")
    @app.get("/dashboard")
    async def serve_dashboard(path: str = ""):
        """Serve the React frontend for the dashboard."""
        index_file = os.path.join(frontend_path, "index.html")
        if os.path.exists(index_file):
            return FileResponse(index_file)
        return {"error": "Frontend not built. Run 'cd frontend && npm run build'"}
    
    # Serve frontend routes directly (for direct navigation)
    @app.get("/research")
    @app.get("/create") 
    @app.get("/video/{video_id}")
    @app.get("/videos")
    @app.get("/library")
    @app.get("/users")
    @app.get("/jobs")
    @app.get("/api-keys")
    @app.get("/settings")
    async def serve_frontend_routes():
        """Serve the React frontend for direct route navigation."""
        index_file = os.path.join(frontend_path, "index.html")
        if os.path.exists(index_file):
            return FileResponse(index_file)
        return {"error": "Frontend not built. Run 'cd frontend && npm run build'"}
else:
    logger.warning(f"Frontend not found at {frontend_path}. Run 'cd frontend && npm install && npm run build' to build the frontend.")

# Security monitoring endpoint
@app.get("/security/stats")
@limiter.limit("10/minute")
async def get_security_stats(request: Request, _: str = Depends(get_api_key)):
    """Get current security statistics (requires API key)."""
    # Get middleware instance
    from app.middleware.security import SecurityMiddleware
    instance = SecurityMiddleware.get_instance()
    if instance:
        return instance.get_security_stats()
    return {"error": "Security middleware not found"}

# Serve landing page at root
@app.get("/")
async def serve_landing():
    """Serve the landing page."""
    frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "frontend", "dist")
    index_file = os.path.join(frontend_path, "index.html")
    if os.path.exists(index_file):
        return FileResponse(index_file)
    return {
        "message": "Welcome to Ouinhi API",
        "description": "AI-powered media generation and processing API",
        "docs": "/docs",
        "dashboard": "/dashboard",
        "admin": "/admin",
        "api": "/api",
        "status": "operational",
        "note": "Frontend not built. Run 'cd frontend && npm run build' to build the frontend."
    }


@app.on_event("startup")
async def startup_event():
    """Run startup events."""
    # Initialize database service with comprehensive setup
    from app.database import database_service
    try:
        logger.info("🚀 Starting Ouinhi API - Database initialization")
        await database_service.initialize()
        await database_service.create_tables()
        await database_service.update_enums()
        logger.info("✅ Database service initialized successfully")
        
        # Initialize music files if needed
        try:
            import subprocess
            result = subprocess.run(["/app/scripts/init_music.sh"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("🎵 Music files initialized successfully")
            else:
                logger.warning(f"⚠️ Music initialization warning: {result.stderr}")
        except Exception as e:
            logger.warning(f"⚠️ Music initialization failed: {e}")
        
        # Create initial admin user if none exists
        try:
            from app.services.user_service import user_service
            users_result = await user_service.list_users(limit=1)
            if users_result['pagination']['total_count'] == 0:
                await user_service.create_user({
                    'username': 'admin',
                    'email': '<EMAIL>',
                    'full_name': 'System Administrator',
                    'password': 'admin123',  # Change this in production!
                    'role': 'admin',
                    'is_active': True
                })
                logger.info("👤 Created initial admin user: <EMAIL> (password: admin123)")
            else:
                logger.info(f"👥 Found {users_result['pagination']['total_count']} existing users")
        except Exception as e:
            logger.warning(f"⚠️ User service check failed: {e}")

        # Verify video service integration
        try:
            from app.services.video_service import video_service
            stats = await video_service.get_video_stats()
            logger.info(f"📊 Video library status: {stats['total_videos']} videos available")
        except Exception as e:
            logger.warning(f"⚠️ Video service check failed (this is OK on first run): {e}")
            
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        logger.error("💡 This may cause video library persistence issues")
    
    # Start background scheduler service
    try:
        from app.services.scheduler_service import scheduler_service
        await scheduler_service.start()
        logger.info("✅ Background scheduler started successfully")
    except Exception as e:
        logger.error(f"❌ Failed to start background scheduler: {e}")
        logger.error("💡 Automatic job cleanup will not work")
        # Don't raise - allow app to start even if DB connection fails temporarily
        # raise
    
    # Import and initialize Redis service
    from app.services.redis_service import redis_service
    try:
        await redis_service.connect()
        logger.info("Redis service connected successfully")
    except Exception as e:
        logger.warning(f"Redis connection failed: {e}. Continuing without Redis.")
    
    # Import job queue service to initialize it
    from app.services.job_queue import job_queue
    
    # Set Redis service for job persistence (still used for caching)
    job_queue.set_redis_service(redis_service)
    
    # Recover jobs from database
    try:
        await job_queue.recover_jobs()
        logger.info("Job recovery completed")
    except Exception as e:
        logger.warning(f"Job recovery failed: {e}")
    
    logger.info("Job queue initialized with max size: %d", job_queue.max_queue_size)


@app.on_event("shutdown")
async def shutdown_event():
    """Run shutdown events."""
    # Stop background scheduler service
    try:
        from app.services.scheduler_service import scheduler_service
        await scheduler_service.stop()
        logger.info("Background scheduler stopped")
    except Exception as e:
        logger.warning(f"Error stopping scheduler: {e}")
    
    # Close database service
    from app.database import database_service
    try:
        await database_service.close()
        logger.info("Database service disconnected")
    except Exception as e:
        logger.warning(f"Error disconnecting database: {e}")
    
    # Disconnect Redis service
    from app.services.redis_service import redis_service
    try:
        await redis_service.disconnect()
        logger.info("Redis service disconnected")
    except Exception as e:
        logger.warning(f"Error disconnecting Redis: {e}")
    
    # Import job queue service
    from app.services.job_queue import job_queue
    
    # Clean up resources
    for task in job_queue.processing_tasks.values():
        task.cancel()
    
    logger.info("All job processing tasks cancelled")


if __name__ == "__main__":
    import uvicorn
    
    # Use multiple workers to better handle concurrent requests
    # Workers should be 2-4 times the number of CPU cores
    workers = int(os.environ.get("UVICORN_WORKERS", 4))
    
    # In development, use a single worker with reload=True
    if os.environ.get("DEBUG", "False").lower() == "true":
        uvicorn.run(
            "app.main:app", 
            host="0.0.0.0", 
            port=8000, 
            reload=True
        )
    else:
        # In production, use multiple workers with no reload
        uvicorn.run(
            "app.main:app", 
            host="0.0.0.0", 
            port=8000, 
            workers=workers
        ) 